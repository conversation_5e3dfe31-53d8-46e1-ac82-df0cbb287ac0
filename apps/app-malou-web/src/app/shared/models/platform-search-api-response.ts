export interface PlatformSearchApiResponse {
    list: PlatformSearch[];
    pagination?: SocialIdsPagination;
}

export interface PermissionsState {
    isValid: boolean;
    missing: string[];
    dataExpiresAt?: number;
}

export interface PlatformSearch {
    name: string;
    locationId: string;
    formattedAddress: string;
    picture: string;
    rating: number;
    socialUrl: string;
    socialId: string;
    access?: PermissionsState;
    accountId?: string;
    accountName?: string;
    apiEndpointV2?: string;
    apiEndpoint?: string;
    pageCategory?: string;
    parentSocialId?: string;
    placeId?: string;
    hasTransitionedToNewPageExperience?: boolean;
    username?: string;
    drnId?: string;
    connectableStatus: ConnectableStatus;
}

export enum ConnectableStatus {
    CONNECTABLE = 'CONNECTABLE', // The venue exists on our provider as a subscription (Hyperline) so it can be connected
    NOT_CONNECTABLE = 'NOT_CONNECTABLE', // The venue exists on Google or Facebook but not as a subscription (Hyperline) so it can't be connected
    ALREADY_CONNECTED = 'ALREADY_CONNECTED', // The venue is already connected
}

export interface SocialIdsPagination {
    total: number;
    count: number;
    limit: number;
    currentPage: number;
    totalPages: number;
    nextPage: string;
    previousPage: string;
}
