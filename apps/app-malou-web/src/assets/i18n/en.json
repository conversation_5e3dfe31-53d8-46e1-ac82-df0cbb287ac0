{"admin": {"access": {"access_to_verify": "Access to verify", "access_update_error": "Failed to update access", "bad_access": "Wrong access", "checked": "Checked on", "copied": "Copied to the clipboard", "failed": "Other error", "invalid_page": "Invalid listing", "login": "<PERSON><PERSON>", "need_review": "To be reviewed", "no_access": "No existing access for this business", "password": "Password", "password_error": "Failed to copy password", "pending": "Pending", "search_business": "Search for a business", "show_treated": "Show checked", "status": "Status", "status_updated": "Status updated", "success": "Success", "type": "Type", "unclaimed_page": "Unclaimed listing", "updated_status": "Status updated", "verified": "Functional"}, "active": "Active", "admin": "Admin", "ai_completion": "Completed by AI", "business": "Business", "category": "Category", "client_update": "Client updated on", "delete": "Delete", "edit": "Modify", "editor": {"error": "Error", "language_not_supported": "The selected language is not supported", "no_changes": "No changes made", "select_language": "Change language", "success": "Success", "update_will_be_effective": "The update will be effective in a few moments", "validate": "Confirm"}, "error": "Error", "error_happened": "An error has occurred", "name": "Name", "navbar": {"access": "ACCESS", "access_and_updates": "ACCESS & UPDATE", "developer_settings": "Developer settings", "editor": "EDITOR", "nfc": "TOTEMS", "notifications": "NOTIFICATIONS", "organizations": "ORGANIZATIONS", "permissions": "Permissions", "restaurants": "BUSINESS", "update": "UPDATE", "users": "USERS"}, "nfcs": {"active": "Enable", "add_nfc_chip": "Add an NFC chip", "add_sticker": "Add a sticker", "add_totem": "Add a totem", "all_restaurants_have_sticker": "All businesses already have a sticker, you cannot create a new one.", "chip_name": "Chip number", "chip_number": "Chip #{{ chipName }}", "download_qr_code": "Download QR code", "download_sticker": "Download the sticker", "edit_nfc_chip": "Edit {{name}}", "edit_sticker": "Edit sticker", "load_from_sheet_modal": {"failed_imports_nfc_id": "NFC ID", "failed_imports_reason": "Error", "failed_imports_restaurant_id": "Restaurant ID", "failed_imports_restaurant_name": "Restaurant name", "failed_imports_title": "Some NFCs could not be inserted/created", "invalid_form": "Invalid sheet link or sheet number", "restaurants_not_found": "No restaurants found", "sheet_link": "Google Sheet link", "sheet_link_error": "Please insert a Google Sheets link", "sheet_number": "Page number", "sheet_number_error": "Please indicate the page number (positive number)", "title": "Insert nfcs"}, "name": "Name", "nfc_already_exist": "NFC already exists", "nfc_delete": {"=1": "NFC <b>{{ chipName }}</b> will be deleted", "other": "NFCs <b>{{ chipName }}</b> will be deleted"}, "nfc_successfully_created": "NFC created!", "nfc_successfully_updated": "NFC updated!", "no_totem_description_admin": "There's no totem that matches your search.", "no_totem_description_basic": "Get in touch with your <PERSON>ou contact to order your magic totem.", "no_totem_title_admin": "No telephone, no magic totem", "no_totem_title_basic": "You don't have a magic totem yet", "notes": "Notes", "redirection": "Redirection", "redirection_link": "Link", "restaurant": "Business", "scan_me": "Scan me!", "stars_redirected": "Stars redirected", "stars_redirected_long_text": "Redirect customers to the public platform for reviews", "sticker_already_exist": "Sticker already exists", "sticker_qr_code_filename": "Sticker - QR Code", "sticker_successfully_created": "Sticker created!", "sticker_successfully_updated": "Sticker updated!", "stickers": {"powered_by": "Powered by"}, "totem_name": "Totem name", "totems": "Totems", "type": {"load_nfcs_from_sheet": "From a Google Sheet file", "sticker": "<PERSON>er", "title": "Type", "totem": "NFC support"}}, "notifications": {"active": "Active", "add_translations": "Add translations", "all_businesses": "All businesses", "all_categories": "All categories", "businesses": "Businesses", "businesses_list": "Businesses list", "categories": "Categories", "end_date": "End date", "message": "Message", "notifications": "Notifications", "start_date": "Start date", "title": "Title"}, "organization": "Organization", "organizations": {"add_users": "Add users", "businesses": "Businesses", "businesses_list": "Businesses list", "created_at": "Created on", "delete_organization_confirmation": "The organization will be deleted and this will affect the associated users", "id": "id", "limit": "Limit", "name": "Name", "organization_has_restaurants": "This organization possesses businesses", "organizations": "Organizations", "users": "Users", "users_list": "Users list"}, "platform": "Platform", "platform_management": {"business": "Business", "no_pending_access": "No pending access", "platform": "Platforms"}, "profile": {"about": " about ", "alerts": {"platform_disconnected": {"description": "Receive an email when a platform connection breaks", "title": "Alerts you when a platform is disconnected"}, "post_suggestion": {"description": "Receive an email when an event approaches and you have not posted in a while", "title": "Calendar event incoming"}, "reminders": "Reminders", "review_reminder": {"description": "Negative reviews awaiting reply", "title": "Negative reviews awaiting reply"}, "review_reply_reminder": {"description": "Negative reviews awaiting reply", "title": "Negative reviews awaiting reply"}, "special_hour": {"description": "Receive an email as a holiday approaches to update your opening hours", "title": "Holiday opening hours"}, "summary": {"description": "Receive a summary email every Tuesday of the notifications you missed", "title": "Unread notifications"}, "title": "My Notifications", "untreated_notifications": "Receive an email as soon as you are tagged", "untreated_notifications_description": "An email whenever you are tagged by another user on a post"}, "app_language": "Malou app language", "conditions": "Terms of Service", "daily_reviews": "Receive a daily review report by email", "daily_reviews_description": "A recap email with all Customer Reviews received daily on the selected businesses", "download_report_create_report_before_download": "Choose businesses to be able to download your report.", "download_report_subtitle": "A PDF with the Customer Reviews received over the chosen period, for the same businesses selected as for the daily report.", "download_report_title": "Download review report", "edit": "Edit", "error": "Error", "error_update_user_settings": "An error occurred while updating your settings", "file_size_limit": "Your file can not exceed 61 MB", "file_too_large": "File too large!", "generate_report": "Generate my report", "information": "Information", "messages_notification_description": "A summary email for each new message received on Google for the selected businesses", "messages_notifications": "Receive email notification for Google messages", "my_profile": "Profile", "my_report": "See my report", "no_restaurant": "No business", "profile_updated": "Profile Updated", "restaurants": "Businesses", "save": "Save", "select_restaurants": {"error": "Unknown error", "error_text": "Your email notification alert could not be activated, please try again or contact customer service.", "search": "Find a business", "selected": "<strong> {{ selectedRestaurants }} </strong> selected", "success": "Success", "success_text": "Your email notification alert has been activated! \nYou will receive this email on the same address as your MalouApp login email!", "unknownError": "An unknown error has occurred, please try again or contact customer service.", "validate": "Confirm"}, "sign_out": "Sign out", "toggle": {"no": "No", "yes": "Yes"}, "unknown_error": "An error has occurred, our teams are trying to resolve it, please try again later", "unsubscribed_fields": {"feedbacks": "feedbacks", "messages": "Google Messages", "reports": "daily review reports"}, "unsubscribed_successful": "You have unsubscribed", "unsubscribed_successful_text": "You have successfully unsubscribed from the <PERSON><PERSON><PERSON><PERSON> emails", "your_profil": "Your profile"}, "report-settings": {"title": "My reports"}, "restaurants": {"active": "Active", "add_managers": "Add managers", "address": "Address", "ai_completion": "Build with AI", "booster_pack": "Booster Pack", "boosters": "Boosters", "brand_account": "Brand account", "businesses": "Businesses", "delete_restaurant_confirmation": "The business will be deleted on the MalouApp as well as its platforms, photos and reviews for all the managers of the business", "functionalities": "Functionalities", "id": "id", "limit_reached": "You cannot activate this business because the organization it belongs to has already reached its maximum!", "management": "Management", "managers": "Managers", "managers_list": "Managers list", "name": "Name", "orga_limit_reached": "You cannot make this change because the organization {{ organization }} has already reached its limit!", "organization": "Organization", "roi": "ROI", "semantic_analysis": "Semantic Analysis", "yext": {"feature": "Update via yext", "modal": {"creation": {"description": "Warning, enabling Yext commits us for at least 30 days for this business", "title": "Are you sure you want to enable <PERSON><PERSON><PERSON> for this business ?"}, "delete": {"title": "Are you sure you want to disable <PERSON><PERSON><PERSON> for this business ?"}}}}, "status_not_ok": "Unknown error, please try later or contact customer service.", "unknown_error": "An error has occurred, our teams are trying to resolve it, please try again later", "update": {"access": "Access", "attributes": {"foursquare": {"accepts_reservations": "Réservation", "american_express": "American Express", "discover": "Discover", "free_wi_fi": "Wifi = Yes", "happy_hour": "Happy Hour", "has_terrace": "Terrasse", "pay_credit_card_types_accepted": "Visa / Mastercard", "serves_beer": "Bière", "serves_breakfast": "<PERSON> déjeuner", "serves_brunch": "Brunch", "serves_cocktails": "Cocktails", "serves_dessert": "Dessert", "serves_dinner": "<PERSON><PERSON><PERSON>", "serves_lunch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "serves_wine": "Vin"}, "lafourchette": {"american_express": "American Express", "can_be_privatized": "Can be privatized", "cash_only": "Cash Only", "diner_club_card": "Diner Club Card", "halal": "<PERSON><PERSON>", "has_air_conditioning": "Air Conditioning", "kids_menu": "Kids menu", "locally_produced": "Locally produced", "open_late": "Open late", "organic_food": "Organic food", "pay_credit_card_types_accepted": "Credit card / Mastercard / Visa", "vegetarian_dishes": "Vegetarian dishes"}, "opentable": {"accepts_sodexo_meal_voucher": "Sodexo", "american_express": "American Express", "diners_club": "Diner Club", "discover": "Discover", "has_counter_service": "Au comptoir", "has_delivery": "<PERSON><PERSON><PERSON>", "has_fireplace": "Cheminée intérie<PERSON>", "has_takeout": "A emporter", "is_wheelchair_accessible": "Accès pour personnes à mobilité réduite", "non_smoking_restaurant": "Non-fumeur", "pay_credit_card_types_accepted": "MasterCard", "serves_beer": "Bière", "serves_cocktails": "Cocktails"}, "resy": {"has_seating_rooftop": "Rooftop dining", "jcb": "JCB", "welcomes_children": "Kids menu"}, "tripadvisor": {"accepts_reservations": "Réservation", "all_you_can_eat": "Buffet", "american_express": "American Express", "discover": "Discover", "drive_thru": "Drive", "electronic_payments": "Paiements électroniques", "free_wi_fi": "Wi-fi gratuit", "has_concerts": "Concerts", "has_delivery": "Service de livraison", "has_play_area": "Aires de jeux", "has_seating": "Places assises", "has_takeout": "Plats à emporter", "has_terrace": "Terrasse", "highchairs_available": "Chaise hautes disponibles", "is_wheelchair_accessible": "Accessible en fauteuil roulant", "non_smoking_restaurant": "Restaurants non-fumeurs", "pay_credit_card_types_accepted": "Visa / Mastercard", "serves_alcohol": "Sert de l'alcool", "serves_beer_and_wine": "Bières & vins", "serves_breakfast": "<PERSON> déjeuner", "serves_brunch": "Brunch", "serves_dinner": "<PERSON><PERSON><PERSON>", "serves_late": "Ouvert tard", "serves_lunch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sports_bar": "Bar sportifs", "welcomes_dogs": "<PERSON><PERSON> acceptés", "welcomes_family": "Style familial"}, "yelp": {"accepts_cash": "Accepts Cash", "accepts_credit_cards": "Accepts Credit Cards", "all_you_can_eat": "All You Can Eat", "beer_and_wine_only": "Beer & Wine Only", "dogs_allowed": "Dogs Allowed", "free_wi_fi": "Wifi = free", "full_bar": "Full Bar", "happy_hour_specials": "Happy Hour Specials", "has_counter_service": "Has Counter Service", "has_delivery": "Can Order Delivery", "has_high_chairs": "High Chairs", "has_play_area": "Play Area", "has_takeout": "Can Order Take-out", "has_tv": "Has TV", "is_owned_by_women": "Women-owned", "is_wheelchair_accessible": "Is Wheelchair Accessible", "kids_menu": "Kids Menu", "outdoor_seating": "Outdoor Seating", "paid_wi_fi": "Wifi = paid", "rooftop_seating": "Rooftop Seating", "takes_reservations": "Takes Reservations"}}, "both": "Access and updates", "business_unavailable": "It appears that your business does not have a review on Yelp, so the data cannot be retrieved.", "checked_access": "Access verified?", "compare_modal": {"access_platform": "Go to platform", "checking_update": "Verification of the update", "close": "Close", "copied": "Copied to the clipboard", "descriptions": {"long": "<PERSON>", "short": "Short"}, "field": "Field", "from_platform": "Found from platform", "from_restaurant_app": "Reference on MalouApp", "modified_fields": "Information updated by user", "no_update": "No updates found", "not_updated_data": "The platform data is not up to date", "other_fields": "Other information", "update_information_on_platform": "Information to be updated on {{ platform }}", "updated_data": "The platform data is not up-to-date"}, "compared_modal": {"copied": "Copied!"}, "completed": "Completed", "date": "Date", "display": "Display", "error": "Error", "failed_recovery": "Information recovery failed", "in_progress": "In progress", "information": "Information", "login": "<PERSON><PERSON>", "nice_fields": {"address": "Address", "attributes": "Attributes", "category": "Main category", "category_list": "Secondary categories", "country": "Country", "description": "Description", "is_claimed": "Delivered page", "is_closed_temporarily": "Temporarily closed", "locality": "City", "logo": "Logo", "menu": "<PERSON><PERSON>", "menu_url": "Menu url", "name": "Business name", "opening_date": "Opening date", "phone": "Phone", "postal_code": "Postal code", "regular_hours": "Opening hours", "special_hours": "Special hours", "website": "Website"}, "no_update": "No existing updates for this property", "password": "Password", "password_error": " Failed to copy password", "profile_updated": "Profil updated", "search_business": "Search for a business", "show_treated": "Show checked", "success": "Updated", "unknown_error": "Unknown error, please try later or contact customer service.", "update": "Update", "update_count": "Number of Updates", "update_status": "update status:", "updates_comparison": {"current_value": "Current value", "field_name": "Key", "keys": {"administrativeArea": "Region", "attribute": "Attribute", "categoryName": "Category", "country": "Country", "coverUrl": "Cover picture", "formattedAddress": "Address", "isClosedTemporarily": "Temporarily closed", "lat": "Latitude", "link": "Link", "lng": "Longitude", "locality": "City", "logoUrl": "Logo", "longDescription": "Long description", "menuUrl": "Menu URL", "name": "Name", "openingDate": "Opening date", "phone": "Phone number", "postalCode": "Postal code", "regionCode": "Country code", "route": "Street", "secondaryCategoriesNames": "Secondary categories", "shortDescription": "short description", "streetNumber": "Street number", "website": "Website"}, "no_update_to_do": "No update to do", "platform_keys": {"foursquare": {"formattedAddress": "Location", "logoUrl": "Photo", "longDescription": "Basic info", "name": "Basic info", "regularHours": "Hours", "specialHours": "Events"}, "lafourchette": {"formattedAddress": "Restaurant address", "name": "Restaurant name", "phone": "Restaurant public phone", "website": "Restaurant website"}, "opentable": {"categoryName": "Cuisine principale - obligatoire", "phone": "Numéro de téléphone", "regularHours": "Heures d'ouverture", "secondaryCategoriesNames": "Cuisines supplémentaires", "website": "Site internet"}, "resy": {"categoryName": "Type de cuisine", "longDescription": "À propos du restaurant"}, "tripadvisor": {"formattedAddress": "Localisation", "longDescription": "Description de l'établissement", "menuUrl": "<PERSON><PERSON>", "name": "Nom de l'établissement", "phone": "Numéro de téléphone", "website": "<PERSON><PERSON><PERSON>"}, "yelp": {"categoryName": "Catégories et services", "isClosedTemporarily": "Fermeture temporaire", "longDescription": "Historique", "menuUrl": "Menu link", "name": "Nom du commerce", "openingDate": "En quelle année avez-vous démarré votre activité ?", "phone": "Numéro de téléphone", "regularHours": "Heures d'exploitation", "secondaryCategoriesNames": "Catégories et services", "shortDescription": "Spécialités", "specialHours": "Prochains horaires exceptionnels", "website": "Lien du site web"}}, "previous_value": "Previous value"}}, "updates": "Updates", "users": {"active": "Active", "businesses": "Businesses", "businesses_count": "Businesses count", "businesses_limit": "Businesses limit", "casl_role": "Role on organizations", "change_password": "Change password", "default_lang": "Default language (handle with care)", "email": "Email", "first_name": "First name", "id": "id", "impersonate_user": "Connect as", "name": "Name", "new_password": "New password", "organizations": "Organizations", "organizations_list": "List of organizations", "password": "Password", "role": "Role", "users": "Users"}, "validate": "Confirm", "warning": "Warning", "yes": "Yes", "yes_delete": "Yes, delete"}, "aggregated_posts_insights_table": {"top_3_publications_title": "Top 3 of your publications that reached peaks"}, "aggregated_statistics": {"boosters": {"scans": {"direct_scans": "Direct scans", "scan_count": "Scan count", "scans": {"=1": "scan", "other": "scans"}, "totems": "Totems", "wheel_of_fortune": "Wheel of fortune"}, "totems": {"private_review_count": {"title": "Number of private reviews"}, "ratings": {"=0": "Review without rating", "=1": "1 star review", "other": "# star review"}, "review_count": {"title": "Number of reviews collected"}, "title": "Totems", "totem_estimated_reviews": "Estimated reviews", "totem_private_reviews": "Private reviews"}, "wheel_of_fortune": {"estimated_review_count": {"business": "Business", "evolution": "Evolution", "reviews": "Reviews", "title": "Estimated number of reviews collected"}, "gifts_distribution": {"business": "Business", "distribution": "Distribution", "retrieved": "Retrieved", "winning": "Winners"}, "gifts_kpis": {"retrieved": "Gifts collected", "winning": "Winners"}}}, "common": {"download_statistics": "Download statistics"}, "download_insights_modal": {"please_select_dates": "Please select a date range"}, "e_reputation": {"review_ratings_average": {"average_rating_of_reviews_received": "Average review score"}, "reviews_analysis": {"activate_semantic_analysis": "Enable analytics in your reviews for each business", "breakdown_of_categories_by_business": "Breakdown of categories by business", "edit_filters": "Please modify the date filter or try again later", "feeling": "{{sentimentsNum}} {{sentiment}} sentiments ", "feelings": "sentiments", "negative": "negatives", "negative_sentiments": "Negative sentiments", "positive": "positive", "positive_sentiment_percentage": "Distribution of positive sentiments by category", "positive_sentiments": "Positive sentiments", "positive_sentiments_table": {"global": "Global", "no_top_topics": "There is no top topic for the moment ...", "restaurant": "Establishment", "total_sentiments": "Total sentiments"}, "redirection_warning": "(You will be redirected to the aggregated reviews page)", "semantic_analysis": "Semantic analysis of reviews", "semantic_analysis_details_by_business": "Breakdown by business of the semantic analysis of reviews", "semantic_analysis_disabled": "Semantic review analysis is not enabled", "semantic_analysis_not_available": "The semantic analysis of reviews is not available during this period"}, "reviews_count": "Number of reviews", "reviews_on_period": "Reviews over the period"}, "errors": {"change_filters": "Change filters", "error_occurred_try_again": "An error occurred while loading the data, please try again later.", "gmb_data_does_not_exist_for_business_restaurants": "SEO data does not exist for brand pages: {{restaurants}}.", "gmb_fetching_error": "An error occurred while retrieving Google data for restaurants: {{restaurants}}. \nEdit your filters or check the connection to the platforms.", "no_data_change_filters": "No data for this period for the selected businesses, please modify your filters.", "platforms_not_selected": "Please select at least one platform", "requires_permissions": "Please reconnect the {{platformName}} platform, making sure you accept all permissions.", "restaurants_not_selected": "Please select at least one business", "select_at_least_2_businesses": "Please select at least 2 businesses", "server_is_not_responding": "The server is no longer responding...🙈", "wrong_calculations": "No data to show!"}, "filters": {"fetch_platforms_error": "Error fetching selected business platforms, please try again"}, "please_select_restaurants": "Please select at least one business", "seo": {"gmb_actions": {"actions_google": "Actions - Google My Business", "actions_on_period": "Actions over the period", "itinerary_request": "Route request", "phone_call": "Phone call", "sort_by": "Sort", "user_actions_number": "The number of times people have taken an action on your Google Page", "visit_website": "Website visit"}, "gmb_impressions": {"impressions_google": "Appearances - Google My Business", "impressions_maps": "Google Maps Appearances", "impressions_maps_on_period": "Google Maps appearances on period", "impressions_search": "Google Search Appearances", "impressions_search_on_period": "Google Search appearances on period", "total_impressions_maps_tooltip": "The number of times your business appeared on Google Maps", "total_impressions_search_tooltip": "The number of times your business appeared on Google Search"}, "keywords": {"all_businesses": "All venues", "all_keywords": "All keywords", "branding": "Notoriety search", "cant_get_data": "Unable to retrieve information related to google for this business", "common_keywords": "Common keywords", "discovery": "Discovery search", "google_ranking": "Google ranking", "google_ranking_on_keyword": "Google position on your keywords", "google_search_keywords": "Google searches", "in_common": "In common", "no_data_for_brand_business": "No information for this brand page", "restaurant": "Business", "top20": "Top 20", "top3": "Top 3", "view_keywords_list": "View table"}, "top_keyword_search_impression": {"modal": {"branding_title": "Businesses that work on the keyword: {{ keyword }}", "discovery_title": "Businesses found by searching: {{ keyword }}"}, "table": {"average_apparitions": "Average appearances", "average_apparitions_tooltip": "Recent keyword, still unknown evolution", "businesses": "Businesses", "keyword": "Keywords", "n_businesses": "{{count}} venues"}, "top_branding_keywords": "Your keywords with the most appearances", "top_discovery_keywords": "Your customers' Google searches with the most visits generated"}}, "social_networks": {"business": "Business", "default_error_text": "Something went wrong, please try again later.", "default_error_title": "Houston, we have a problem!", "edit_filters": "No data on this period for the selected businesses. \nPlease modify your filters.", "edit_permissions": "Please edit settings", "engagement_rate": "Engagement rate", "followers": "Followers", "impressions": "Impressions", "missing_permissions": "Permissions for {{platformName}} are missing to access this data.", "modify": "Edit", "no_insights_found": "No insight found for {{platformName}}", "platform_not_connected": "The {{platformName}} platform is not connected.", "posts": "Posts", "social_networks": "Social Media", "unknown_error": "Unknown error", "wrong_calculations": "No data to show!"}}, "aggregated_statistics_pdf": {"boosters": {"title": "Aggregated Boosters insights"}, "common": {"restaurants": "Businesses: {{ restaurants }}"}, "e-reputation": {"title": "Aggregated E-Reputation insights"}, "seo": {"title": "Aggregated SEO insights"}, "social_networks": {"title": "Aggregated Social Network insights"}}, "ai": {"general": {"general": "General", "language": "Language", "restaurant_name": "Name of your business"}, "posts": {"details": "Configure the default creation of your posts with artificial intelligence", "edit": {"tabs": {"seo": "SEO", "social_networks": "Social networks"}}, "emoji": "<PERSON><PERSON><PERSON>", "length": "Caption length", "name": "Posts", "personalize": "Customize my AI assistant", "preview_placeholder": "Example: our banana cake, with the list of ingredients and a catchphrase to come and taste it", "preview_title": "Generate a caption about...", "prompt": "Advanced Prompt", "prompt_placeholder": "Example: Always end with a joke", "tone": "<PERSON><PERSON>", "tone_length_error": "You must select at least one tone", "upsert": {"title": "Settings for your AI Post Assistant"}}, "reviews": "Customer Reviews"}, "answer_reviews": {"no_reviews": "No reviews available for these filters."}, "app_select_restaurants": {"business": "Businesses", "no_business_found": "No businesses found", "your_business": "Your Businesses"}, "archive": {"show_archived": "Archived", "show_not_archived": "Not archived"}, "automations": {"ai": {"title": "🤖 Your Personalized AI Assistant"}, "reviewReplies": {"automation_disabled": "Automation disabled", "automation_enabled": "Automation enabled", "automation_partially_enabled": "Partially activated", "automations_with_comment": "Automation of reviews with comments", "automations_without_comment": "Automation of reviews without comments", "available_templates_tooltip": "You have {{templatesLength}} response templates matching {{ratingCategory}} {{withComment}} comment, they will be used randomly.", "edit": {"ai_hard_limit_reached": "Oops! \nYou no longer have credit to use AI", "ai_hard_limit_reached_subtext": "The suggested answers will be available again when your credits are renewed", "answerWith": "Reply with", "auto_send_for_platforms": "Automatically send for the following platforms:", "credits": "credits", "none": "None", "platforms_not_connected": "Oops! \nYou have no platform connected", "platforms_not_connected_subtext": "To connect the automatisable platforms (Google, Deliveroo, UberEats, Zenchef), you must go to", "platforms_not_connected_subtext_link": "the platforms", "platforms_not_selected_reply_generation": "Reviews from non-selected platforms will have a reply suggestion to be validated manually.", "recommended": "recommended", "replyMethod": {"ai": "Artificial Intelligence", "templates": "Templates"}, "templates": {"title": "The templates concerned ({{templatesSelected}}/{{templatesLength}} enabled)"}, "templates_not_found": "Oops! You don't have any matching Review templates", "templates_not_found_subtext": "To create one, you must go to", "templates_not_found_subtext_link": "Resources/Review templates", "templates_not_selected": "Please select at least one template!"}, "enable": "Enable", "keywords": {"confirm": "Choose my keywords", "text": "To create your templates, we strongly recommend that you choose your keywords beforehand in order to optimize them", "title": "You have not yet defined your keywords"}, "ratingCategories": {"REVIEW_1_2": "1 & 2 stars", "REVIEW_3": "3 stars", "REVIEW_4": "4 stars", "REVIEW_5": "5 stars"}, "replyMethod": {"AI": "Enabled with AI", "TEMPLATES": "Enabled with review templates", "disabled": "Disabled"}, "reviewsWithComment": "Review with a comment", "reviewsWithoutComment": "Review without a comment", "subtitle": "Responses to reviews will be published starting one hour after the review is received.", "title": "Customer Reviews", "withComment": {"WITHOUT_COMMENT": "without", "WITH_COMMENT": "with"}}, "review_intelligent_subject": {"discrimination": "Discrimination", "discrimination_subtext": "racism, homophobia/transphobia, religion", "duplicate": {"restaurant_selection": {"subtitle": "Select the establishments to which you want to duplicate your subjects at risk"}, "success": "The duplication of your risk subjects has been carried out"}, "hygiene": "Hygiene", "hygiene_subtext": "illness (vomiting/diarrhea), hygiene (mold, worms, etc), dirty (toilets, table, etc)", "recipients_error_message": "Add an email address to receive the alert", "subtitle": "We do not treat the reviews containing these subjects, you will receive an email to inform you.", "title": "Risky subjects"}}, "boosters": {"presentation": {"average_rating": {"figures": "4.6", "text": "average rating on reviews received"}, "do_like_our_client": "Shine like our customer", "get_now": "Get it now", "gift_retrieved": {"figures": "50%", "text": "of rewards have been collected"}, "learn_more": "More information", "more_reviews": {"figures": "5X", "text": "more Google reviews"}, "positive_reviews": "<span class=\"malou-text-12--semibold\"> Collect only positive reviews</ span> with our automatic filter.", "retain_your_customers": "<span class=\"malou-text-12--semibold\">Boost customer loyalty</span> by giving them a chance to win rewards with the Wheel of Fortune.", "reviews_per_month": {"figures": "+900", "text": "Customer Reviews in just one month"}, "subtitle": "Malou's Boosters are a simple, fun, physical and digital solution for improving your online visibility and building customer loyalty.", "title": "Take your reviews and followers to the next level", "totems_and_stickers": "Encourage your customers to <span class=\"malou-text-12--semibold\">leave reviews using the 3 included totems and 50 QR code stickers.</ span>", "you_will_be_contacted": "Our team will get back to you"}}, "campaigns": {"bulk": {"delete": "Select campaigns to delete"}, "campaign_activated_modal": {"campaign_activated": "Review campaign activated", "client_received": "  customer received your message.", "clients_received": "  customers received your message.", "close": "Close", "congrats": "Congrats!", "more_info_html": "From now on, all future customers, corresponding to the defined audience, will receive a request <strong>24 hours after being added</strong>"}, "campaign_type": {"promotion": "Promotional campaign", "review_booster": "Review booster"}, "campaign_will_be_deleted": "The campaign will be deleted", "campaigns": "Campaigns", "campaigns_will_be_deleted": "The campaigns will be deleted", "campaigns_will_be_deleted_single": "The campaign will be permanently deleted", "cancel": "Cancel", "client_will_be_deleted": "The customer will be deleted", "clients_will_be_deleted": "The customers will be deleted", "combined_actions": "Bulk actions ({{ number }})", "confirm": "Validate the campaign", "confirm_email": {"confirmed": "Your email address has been verified", "require_email_confirm": "The campaign can only be sent once the email has been confirmed", "warning": "Warning", "warning_email": "Your sender email address has not been verified.", "warning_email_details": "An email has just been sent to the address {{ email }}. Click on the email link to validate your address and confirm the sending"}, "contactMode": "Contacted by", "create": {"confirm_email_required": "You must validate your email in the next step to send test emails", "form": {"clients": "{{ nbCustomers }} customers", "clients_text": "<strong>{{ nbSelectedClients }} / {{ nbClients }} clients</strong> affected by your configuration", "confirm_agreement": "I confirm that I have obtained consent for the use of my personal data as part of an opinion request.", "dates": "Date of last visit between", "dates_mobile": "Date between", "from": "From", "html": "Your message", "last_contact": "Minimum number of days since the last solicitation?", "last_contact_placeholder": "Number of days", "mode": "Which sending medium would you like to use?", "name": "What is the name of your campaign?", "name_placeholder": "Your campaign name", "object": "Object", "platform": "Collect reviews for which platform?", "platform_placeholder": "Platform(s)", "redirectFrom": "Redirect customers to the public platform for reviews", "sources": "What source were they added from?"}, "overview": "Overview", "title": "Create a review campaign"}, "create_campaign": "Create campaign", "createdAt": "Date", "date": "Date", "delete": "Delete", "deleted": "Campaigns deleted!", "duplicate": "Duplicate", "edit": "Modify", "email_verification_pages": {"error": "Unknown error", "error_text": "An error has occurred, we could not validate your email address for the campaign.", "validated": "Verified email address", "validated_text": "The sender email address has been verified for your campaign!", "validated_text_next": "You can close this page and go back to your previous MalouApp tab."}, "end_campaign": {"message": "Do you really want to end the campaign?", "title": "End the campaign"}, "error": "Unknown error", "error_already_left_review": "Error", "error_detail": "An error occurred, we could not redirect you to leave a review", "error_detail_left_review": "You have already left a review.", "error_while_loading": "Unknown error while loading campaigns, please try later or contact support service.", "finish": "End", "has_campaign_in_progress_modal": {"message": "You can only have <b>one campaign</b> with status “in progress” at the same time. You have to <b>end the campaign {{ name }}</b> before creating a new one.", "title": "You already have a campaign in progress"}, "import_manual": "Manual import", "infos": "Campaign {{name}} sent the {{date}}: {{platform}}", "mail": "{{number}} mail", "mails": "{{number}} mails", "mails_number": "Emails Sent", "name": "Name", "negative_review": {"error": "Unknown error", "left_review_success": "Thank you for leaving us a review", "ok": "Done", "review_not_created": "An error occurred, we could not save your review", "send": "Send", "sent": "Review sent", "success": "We will look at your review carefully! \n<br/> Do not hesitate to come back to us if we have improved :)", "text": "We are sorry that your time with us did not meet your expectations.<br>Could you take <b>a few seconds</b> to help us improve by describing a little more the circumstances of your visit?<br><br>Thank you so much 🙏", "thanks": "Thanks! \n🙏", "title": "What happened?", "write_review": "Leave your review here .."}, "new_campaign": {"audience_overview": "Audience overview", "audience_step": "📬 Configure audience", "bad_click_here": "Not good 👎: click here ", "business_name": "@Name_business", "campaign_already_exist": "A campaign already exists with this name", "campaign_created": "Campaign sent!", "campaign_name": "What is the name of your campaign?", "cancel": "Cancel", "click_to_verify": "<strong>Click on the link</strong> of the mail to validate your address and confirm the campaign", "client_firstname": "@Customer_Firstname", "client_involved": " impacted customers", "client_lastname": "@Customer name", "clients_involved": " Impacted customers", "clients_last_visit": "Last visit between:", "clients_source": "Which source were they added from?", "consent": "I confirm that I have obtained consent for the use of his personal data in the context of a request for a review.", "contact_mode": "Which sending method would you like to use?", "content_step": "📢 Write message", "days_between_last_contact": "Minimum number of days since last solicitation?", "duplicate_campaign_title": "Duplicate campaign", "email": "Email address", "email_not_sent": "The email could not be sent", "email_not_verified": "Unverified sending email address", "email_sent": "Email sent", "email_verified": "Verified sending email address", "email_verified_text": "The sending email address has been <strong>verified</strong>!", "end": "End", "end_date": "End date", "error": "Unknown error", "expeditor": "Sender", "five_stars": "5 stars only", "four_stars": "4 and 5 stars", "from": "From:", "from_short": "From", "great_click_here": "Great 👍: click here", "how_was_your_experience": "How was your experience?", "mail_object": "Mail object", "message": "Message", "name": "Campaign name", "never": "Never", "next": "Next", "no": "No", "no_more_email": "You don't want to receive emails from {{ restaurantName }} anymore?", "object": "Object", "parameters_step": "⚙️ Set up the campaign", "phone": "Phone", "prefilled_message": "Hello @Customer_Firstname,\n\nThank you for your visit to @Name_business.\n\n\nHow was your experience?", "preview": "Preview:", "previous": "Previous", "quit_anyway": "Quit anyway", "redirect_from": "Redirect customers to the public platform for reviews:", "return": "Back", "send": "Send", "send_test_email": "Send test email", "send_test_email_tooltip": "The test email will be sent to {{email}}", "send_test_sms": "Send test SMS", "send_test_sms_tooltip": "The test SMS will be sent to {{phone}}", "should_receive": "You should receive it shortly", "sms_not_available_yet": "SMS campaigns will soon be added", "some_emails_not_sent": "Some emails could not be sent", "some_emails_not_sent_text": "You can try to relaunch a campaign to send to customers who have not received it yet", "sources": {"malou_file": "<PERSON><PERSON> file", "manual": "Manually added", "the_fork": "The Fork", "wheel_of_fortune": "Wheel of fortune", "zenchef": "Zenchef"}, "start_date": "Start date", "steps": {"settings": "Configure the campaign", "target_settings": "Configure the audience", "write_message": "Write the message"}, "target_platform": "You want reviews for which platform?", "unsubscribe": "Unsubscribe", "validate": "Confirm", "will_lost_campaign": "If you change the page or close this window, the campaign you created will be deleted.", "will_need_verification": "You will have to confirm that you own this email address", "wont_be_saved": "The campaign will be deleted", "wont_be_saved_text": "If you quit this page, your campaign won't be saved", "wont_show_later": "This step will no longer be displayed for future campaigns", "wrong_verification_email": "Verification email could not be sent, please check your email address", "yes": "Yes", "your_message": "Your message", "your_opinion_on": "Your opinion on {{restaurantName}}?🍴😍", "your_review_on": "Your review on {{ restaurantName }}"}, "no_campaigns": "You don't have any campaigns", "no_campaigns_text": "Create a campaign based on your goals", "no_client_modal_message": "To launch a campaign, you must add contacts in the “Resources/Clients” section.", "no_client_modal_title": "Let's launch your first review campaign!", "no_clients_modal": {"add_now": "Do you want to add customers now?", "close": "Close", "needs_clients": "To create a campaign, you need to add customers in Resources/Customers", "no_clients": "You don't have any customers saved yet", "no_later": "No, later", "yes_i_want": "Yes, please"}, "no_match": "It's too quiet... I don't really like it...", "no_match_details": "No campaigns match your search...", "no_results": "Campaign shower", "no_results_details": "Start by creating a campaign according to your objectives", "no_stats": "This information is only available for campaigns created after July 22, 2022", "open_report": "See the report", "platform": "Platform: {{platform}}", "platform_column": "Platform", "platform_text": "Platform", "positive_review": {"on": " on {{ platform }}", "send": "Leave a review", "sent": "Review sent", "text": "We are delighted that you enjoyed your visit with us! Could you take <b>10 secondes</b> to help our business by leaving a review?", "thanks": "<br><br>Thank you very much 🙏", "title": "Thank you 😍"}, "promotion": "Promotional campaign", "report": {"campaign": "The campaign", "campaign_report": "Report for campaign {{ name }} from {{ date }}", "campaign_report_description": "{{ name }} from {{ date }}", "campaign_report_title": "Report for campaign {{name}} from {{date}}", "client_sources": {"lafourchette": {"=1": "{{number}} client from The Fork\n", "other": "{{number}} clients from The Fork \n"}, "malou": {"=1": "{{number}} client from a Malou file\n", "other": "{{number}} clients from a Malou file\n"}, "manual": {"=1": "{{number}} client added manually\n", "other": "{{number}} clients added manually\n"}, "wheel_of_fortune": {"=1": "{{number}} client added from the wheel of fortune", "other": "{{number}} clients added from the wheel of fortune"}, "zenchef": {"=1": "{{number}} client from Zenchef\n", "other": "{{number}} clients from Zenchef\n"}}, "contacted_clients": "Clients contacted", "delivered_emails": "Emails delivered", "negative_reviews_avoided": "Negative reviews avoided", "object": "Subject: ", "positive_click_number_five": "Number of clicks on 5 stars", "positive_click_number_four_five": "Number of clicks on 4 or 5 stars", "positive_reviews": "Positive reviews", "received_reviews": "Responses received", "redirection_platform": "Redirection platform", "response_rate": "Response rate", "response_rate_tooltip": "{{responded}} email responses for {{delivered}} delivered emails", "results": "The results", "results_description": "The results correspond to the stars selected in the email", "sent_mails": "Emails sent", "text": "Body:", "unsubscribed_rate": "Unsubscribed"}, "response_rate": "Response rate", "results": "The results", "review_booster": "Review booster", "search": "Search for a campaign", "select": "Select (0)", "select_all_campaigns": "Select all campaigns", "send": "Mail", "sent_to": "Campaign sent to {{number}} customers", "single_deleted": "Campaign deleted!", "status": {"finished": "<PERSON><PERSON>", "in_progress": "In progress", "status": "Status"}, "status_not_ok": "Unknown error, please try again later or contact customer service", "text": "Text: {{textHTML}}", "type": "Type", "unknown_error": "Unknown error", "unsubscribed_rate": "Unsubscribe", "wheel_of_fortune": "Wheel of fortune", "yes_delete": "Yes, delete", "your_email": "Your email"}, "casl": {"wrong_role": "You do not have permissions to perform this action"}, "checklist": {"tasks": {"adapt_content_to_olympics": {"subtitle1": "Mention in your caption that you offer sports events retransmission", "subtitle2": "Specify if you are nearby touristic places or monuments", "title": "Adapt your content"}, "broadcast_sport_attribute": {"subtitle1": "Fill in the “Suitable for watching sports” characteristic and update on Google", "title": "Indicate in your characteristics if you distribute the tests"}, "collect_reviews_with_booster_pack": {"subtitle1": "Arrange totems in the room", "subtitle2": "Stick stickers with the QR code", "subtitle3": "Install the wheel of fortune (multiplies your collected reviews by 5)", "title": "Collect more reviews with the booster pack"}, "common": {"create": "Create", "modify": "To modify", "try": "To try"}, "confirm_your_hours": {"title": "Confirm your opening hours"}, "create_english_posts": {"subtitle1": "Use AI to translate them", "title": "Create posts in English"}, "foreign_language_keywords": {"subtitle1": "Ex: 'Traditional restaurant', 'Bistrot near Montmartre'", "title": "Change 2 or 3 keywords in a foreign language"}}}, "clients": {"add_clients": "Add customers", "add_clients_list": "Add a customer list", "add_manual": {"accepts_emails": "Accepts to receive emails", "accepts_sms": "Accepts to receive sms", "add_client": "Add", "address": "Address", "cancel": "Cancel", "civility": "Title", "civility_types": {"female": "Ms", "male": "Mr", "other": "Other"}, "client_added": "Customer added!", "client_already_exist": "Already exists in your customer base on the MalouApp", "client_created": "Customer added", "client_updated": "Customer updated", "close": "Close", "country": "Country", "email": "Email address", "email_or_phone": "At least one email address or telephone is required", "errors": {"invalid_email": "<PERSON><PERSON><PERSON>", "required": "Email required"}, "firstname": "First Name", "full_lastname": "Last name", "invalid_email": "The email address provided is not valid", "langs": {"english": "English", "french": "French", "italian": "Italian", "spanish": "Spanish"}, "language": "Spoken language", "last_visit_date": "Date of Last Visit", "last_visit_date_tooltip": "Date of last booking or click and collect", "lastname": "Last Name", "locality": "City", "modify": "Edit", "optional": "(optional)", "other": "Register another customer", "other_infos": "Other information", "phone": "Phone Number", "phone_placeholder": "123-456-XXXX", "postalCode": "Postal Code", "province": "Province", "street": "Street", "update": "Update", "update_client": "Edit a customer", "validate": "Confirm", "visit_count": "--", "visit_count_title": "Number of visits", "visit_count_tooltip": "Number of visits/bookings"}, "add_manually": "Add manually", "bulk": {"delete": "Select customers to delete", "duplicate": "Select customers to duplicate to other businesses"}, "by_hand": "Added manually", "cancel": "Cancel", "cant_duplicate_own_restaurant": "It is not possible to duplicate customers in your own business", "client_will_be_deleted": "The customer will be permanently deleted", "clients": "Clients", "clients_already_exist": "Duplication successful. \nAlready existing contacts have not been duplicated", "clients_duplicated": "Customers have been duplicated in the businesses you have selected", "clients_will_be_deleted": "Customers will be deleted permanently", "combined_actions": "Bulk actions ({{number}})", "create_client": "Add customers", "delete": "Delete", "deleted": "Customer deleted successfully", "deleted_plural": "Customers deleted successfully", "details_about_deletion": "The client(s) will be permanently deleted from the database.", "duplicate_clients_subtitle": "Select the businesses to which you wish to duplicate the selected customer(s)", "duplicate_in_others": "Do you still want to duplicate to the other selected businesses?", "duplicate_other_restaurants": "Duplicate to other venues", "duplicated_clients_modal": {"close": "Close", "confirm": "Yes, update", "duplicated_clients": "contacts from the imported list already exist in your customer base on the MalouApp.", "duplicated_clients_singular": "contact from the imported list already exists in your customer base on the MalouApp.", "error": "Error", "import_file": "Upload a {{source}} customer file ", "keep_or_update": "Do you want to update the old contacts with the new uploaded information?", "keep_or_update_singular": "Do you want to update the old contact with the new uploaded information?", "no": "No", "unknown_error": "An error has occurred, our teams are trying to resolve it, please try again later"}, "duplication_failed": "The duplication failed", "duplication_succeeded": "Successfully duplicated", "edit": "Edit", "email": "E-mail address", "error": "Error", "error_while_loading": "Unknown error while loading customers, please try later or contact support service.", "file": "File", "filters": {"firstName": "First name", "isUnsubscribed": "Unsubscribe", "lastName": "Last name", "lastVisitedAt": "Last visit", "mailsNb": "Mail(s) sent", "ratings": "Note(s) given", "source": "Source"}, "firstname": "First name", "given_stars": "Rating(s)", "given_stars_responsive": "Star(s)", "import_client_file": "Upload a customer file", "import_file": "Import a file", "import_malou": "Malou import ", "import_manual": "Manual import", "import_modal": {"choose_format": "Choose file format", "client_exist": "Existing contact", "client_exist_warning": "{{nb}} contacts from the imported list already exist in your MalouApp customer base.", "client_exist_warning_question": "Do you want to update old contacts with new imported information?", "clients_accept_contact": "I confirm that all imported customers agree to be contacted", "clients_length": "contacts will be added to your customer list on the MalouApp.", "clients_length_singular": "contact will be added to your customer list on the MalouApp.", "confirm": "Confirm", "dont_have_model": "I don't have the template file", "download_file": "Download the Malou format file", "download_malou_file": "Download the Malou format file", "drop_file": "Drag/Drop your customer file", "empty_file_error": "The uploaded file is empty. \nPlease fill it in and try again!", "error": "Error", "format_error": "The uploaded file does not comply with the {{platform}} format. \nPlease try again or", "format_error_link": "upload the file with another format here.", "format_error_link_malou": "Please download the file", "format_error_malou": "The uploaded file does not respect the Malou format.", "format_error_malou_next": "and complete it without modifying the columns.", "have_model": "I have the template file", "import_client_file": "Import your customer file", "import_clients": "Upload your file", "import_from_malou": "Upload the completed Malou format file", "import_from_other": "Upload the {{platform}} customer file", "know_more": "Find out more.", "malou_source_text": "Upload the completed Malou format file with your data.", "next": "Next", "other_format": "Other format", "other_source_text": "You must upload an Excel * or CSV * customer file downloaded from {{platform}}.", "preview_clients": "Here is an overview of your data.", "previous": "Previous", "required_headers": "Mandatory column names", "should_download_file": "To upload your data, complete the Malou file according to the format.", "should_download_file_warning": "Be careful, do not change the order nor the column names.", "show_less": "See less", "show_more": "See more...", "step_2_title": "After downloading your client file {{clientFileType}} in Excel or CSV format you can import it here:", "successful_import": "Your file \"{{ fileName }}\" has been successfully imported", "the_fork": "The Fork", "unknown_error": "An error has occurred, our teams are trying to resolve it, please try again later", "upload_file": "Upload file", "zelty": "Zel<PERSON>", "zenchef": "Zenchef"}, "last_visit": "Last visit date", "lastname": "Last name", "malou_file": "<PERSON><PERSON> file", "manually": "Manually", "no": "No", "no_cancel": "No, cancel", "no_match": "Your first name is <PERSON>, is that correct?", "no_match_details": "No customer matches your search", "no_response_client": "You haven't uploaded customers yet", "no_results": "No clients to be seen yet", "no_results_details": "Add a customer list to easily contact them when creating a campaign", "phone": "Phone number", "search": "Search", "select": "Select (0)", "select_all_clients": "Select all customers", "source": "Source", "status_not_ok": "Unknown error, please try again later or contact customer service.", "the_fork": "TheFork", "unknown_error": "Unknown error", "unsubscription": "Unsubscription", "validate_import_modal": {"close": "Close", "congrats": "Well done!", "import_done": "Upload completed", "import_new_client": "Upload another contact", "import_new_file": "Upload another file", "imported_clients": "new contacts have been added to your customer base on the MalouApp", "imported_clients_singular": "new contact has been added to your customer base on the MalouApp"}, "wheel_of_fortune": "Wheel of fortune", "yes": "Yes", "yes_delete": "Yes, delete"}, "close_without_saving_modal": {"cancel": "Back to editing", "confirm": "Close without saving", "subtitle": "Would you like to continue?", "title": "Be careful, you will lose your changes"}, "comments_modal": {"answer_and_next": "Reply and continue", "answer_to_comment": "Reply to comment", "comment": "Comment", "comment_replied": "Response sent successfully", "error_fetching_mention": "We could not retrieve the mention", "mention_in_comment": "Mention in a comment", "mention_in_post": "Mention in a post", "post": "Post", "reply_again": "Write a new reply", "save_answer": "Save as quick response template", "save_answer_subtitle": "(This template will be added to your quick response list)"}, "common": {"activate": "Enable", "add": "Add", "add_later": "Add later", "add_media": "Add media", "add_now": "Add now", "advanced_options": "Advanced Options", "all": "All", "all_f": "All", "all_languages": "All languages", "answer": "Reply", "archive": "Archive", "are_you_sure": "Are you sure?", "automate": "Automate", "back": "Previous", "beta": "Beta", "brand_account": "Brand account", "business": "Business", "business_account": "Business account", "button": "Button:", "by": "through", "cancel": "Cancel", "close": "Close", "closed": "Closed", "confirm": "Confirm", "connect_platforms": "Connect platforms", "copied": "<PERSON><PERSON>d successfully", "copied_to_the_clipboard": "Copied to the clipboard", "copy": "Copy", "date": "Date", "date_past": "The time is over.", "days": {"=0": "Day", "=1": "Day", "other": "Days"}, "delete": "Delete", "disable": "Disable", "disabled": "Disabled", "download": "Download", "download_file": "Download file", "duplicate": "Duplicate", "duplicate_and_go_next": "Duplicate and go next", "duplicate_to": "Duplicate to...", "edit": "Edit", "email": "E-mail", "enable": "Enable", "enabled": "Enabled", "end_date": "End date", "error": "Error", "false": "False", "favorites": "Favorites", "field_required": "This field is required", "fill": "Fill", "filter": "Filter", "filters": "Filters", "followers": "followers", "from": "from", "from_date_to_date": "From {{ startDate }} to {{ endDate }}", "help": "Help", "here": "Here", "hours": {"=0": "Hour", "=1": "Hour", "other": "Hours"}, "hours_abbrev": "h", "hours_short": "h", "import": "Import", "import_file": "Import file", "invalid_email": "Invalid email address", "invalid_field": "Invalid field", "invalid_time": "Invalid time", "irreversible_actions": "Please note, these changes will be irreversible.", "items_per_page": "Items per page", "langs": {"af": "Afrikaans", "am": "Amharic", "ar": "Arabic", "az": "Azerbaijani", "be": "Belarusian", "bg": "Bulgarian", "bn": "Bengali", "bs": "Bosnian", "ca": "Catalan", "ceb": "Cebuano", "co": "Corsican", "cs": "Czech", "cy": "Welsh", "da": "Danish", "de": "German", "el": "Greek", "en": "English", "eo": "Esperanto", "es": "Spanish", "et": "Estonian", "eu": "Basque", "fa": "Persian", "fi": "Finnish", "fr": "French", "fy": "Frisian", "ga": "Irish", "gd": "Scots gaelic", "gl": "Galician", "gu": "Gujarati", "ha": "Hausa", "haw": "Hawaiian", "he": "Hebrew", "hi": "Hindi", "hmn": "Hmong", "hr": "Croatian", "ht": "Haitian creole", "hu": "Hungarian", "hy": "Armenian", "id": "Indonesian", "ig": "Igbo", "in": "Indonesian", "is": "Icelandic", "it": "Italian", "iw": "Hebrew", "ja": "Japanese", "jw": "Javanese", "ka": "Georgian", "kk": "Kazakh", "km": "Khmer", "kn": "Kannada", "ko": "Korean", "ku": "Kurdish (kurmanji)", "ky": "Kyrgyz", "la": "Latin", "lb": "Luxembourgish", "lo": "Lao", "lt": "Lithuanian", "lv": "Latvian", "mg": "Malagasy", "mi": "<PERSON><PERSON>", "mk": "Macedonian", "ml": "Malayalam", "mn": "Mongolian", "mr": "Marathi", "ms": "Malay", "mt": "Maltese", "my": "Myanmar (burmese)", "ne": "Nepali", "nl": "Dutch", "nn": "Norwegian (nynorsk)", "no": "Norwegian", "ny": "Chichewa", "or": "Odia", "pa": "Punjabi", "pl": "Polish", "ps": "Pashto", "pt": "Portuguese", "pt-BR": "Portuguese", "pt-PT": "Portuguese", "pt-pt": "Portuguese", "ro": "Romanian", "ru": "Russian", "rw": "Kinyarwanda", "sd": "Sindhi", "si": "Sinhala", "sk": "Slovak", "sl": "Slovenian", "sm": "Samoan", "sn": "<PERSON><PERSON><PERSON>", "so": "Somali", "sq": "Albanian", "sr": "Serbian", "st": "<PERSON><PERSON><PERSON><PERSON>", "su": "Sundanese", "sv": "Swedish", "sw": "Swahili", "ta": "Tamil", "te": "Telugu", "tg": "Tajik", "th": "Thai", "tl": "Filipino", "tr": "Turkish", "ug": "Uyghur", "uk": "Ukrainian", "undetermined": "Undetermined", "unknown": "Unknown language", "ur": "Urdu", "uz": "Uzbek", "vi": "Vietnamese", "xh": "Xhosa", "yi": "Yiddish", "yo": "Yoruba", "zh": "Chinese", "zh-CN": "Chinese (simplified)", "zh-TW": "Chinese (traditional)", "zhTW": "Chinese (traditional)", "zu": "Zulu"}, "language": "Language", "langue": "Language", "later": "Later", "link": "Link", "login": "ID", "max_length_helper": "{{count}} characters max", "max_number": "max {{number}}", "maximum": "maximum", "merge": "<PERSON><PERSON>", "minimum": "minimum", "minutes": {"=0": "Minute", "=1": "Minute", "other": "Minutes"}, "minutes_short": "min", "missing_value": "Missing value", "more_filters": "Advanced options", "move": "Move", "must_be_over": "The value must be greater than {{min}}.", "must_be_under": "The value must be less than {{max}}.", "new": "New", "next": "Next", "no": "No", "no_data": "No data", "notifications": "Notifications", "oh": "Oh oh...", "ok": "OK", "open_24h": "open 24h", "opened": "Opened", "option_disabled": "This option is disabled", "or": "Or", "password": "Password", "paste": "Paste", "platform_disconnected": "The platform is offline.", "previous": "Previous", "recipients": "Recipient(s)", "reconnect": "Reconnect", "refer_a_friend": "Refer your friends", "referral": {"win_money": "Claim your $250"}, "refresh": "Refresh", "reinitialize": "Reset", "reinitialize_changes": "Reset Changes", "remove": "Remove", "rename": "<PERSON><PERSON>", "reply": "Reply", "required_field": "This field is required.", "reset": "Reset", "restaurants": "Businesses", "retry": "Retry", "return": "Return", "save": "Save", "search": "Search", "search_address": "Find an address", "seconds": {"=0": "Second", "=1": "Second", "other": "Seconds"}, "see": "See", "see_less": "See less", "see_more": "See more", "seen": "Seen", "select": "Select", "select_all": "Select all", "select_all_with_count": "Select all ({{count}})", "selected_post_in_error": "You cannot duplicate an erroneous post", "send": "Send", "server_error": "Server error, please try again later", "server_is_not_responding": "The server no longer answers ... 🙈", "soon_available": "Coming soon", "sort": "Sort", "sort_by": "sort by", "sorted_by_order": "Sorted by order", "start_date": "Start date", "status": "Status", "synchronize": "Synchronize", "synchronizing": "Synchronization in progress", "time": "Time", "to": "to", "to_bis": "to", "to_other_venues": "To other venues", "translate": "Translate", "true": "True", "unanswered": "Not answered", "unarchive": "Unarchive", "understood": "I understand", "unknown_error": "Oops, an error has occurred", "unselect_all_with_count": "Unselect all ({{count}})", "until": "until", "until_short": "until", "update": "Update", "upload_from_computer": "From my computer", "upload_from_gallery": "From my gallery", "upload_from_phone": "From my phone", "validate": "Validate", "verify": "Check", "view_by": "View by", "warning": "Warning", "wrong_format_example": "Bad format, example: {{ example }}", "yes": "Yes", "yes_delete": "Yes delete"}, "credentials": {"missing_organization": {"text": "You are currently not bound to an organization. Please contact <PERSON><PERSON> to solve the issue!", "title": "Missing organization"}}, "dashboard": {"calendar": {"add_event": "Add Event", "bank_holiday": "Careful, it is a bank holiday! \n Change your special hours if necessary", "bank_holiday_text": "Change your special hours if necessary.", "bank_holiday_title": "Careful, it is a bank holiday!", "calendar": "Calendar", "choose_country": "Select the country of events", "choose_event_lang": "For which country do you want to see the events?", "could_not_open_post": "An error occurred while opening the post", "create_in_seo": "Create a local SEO post", "create_in_social": "Create a social media post", "create_post": "Create post", "create_reel": "Create a reel", "create_reel_or_tiktok": "Create a Reel/Tik<PERSON>ok", "create_story": "Create a story", "delete": "Delete", "duplicate": "Duplicate", "edit": "Edit", "edit_hours": "Change hours", "error": "Error", "error_occurred": "Oops, an error has occurred.", "example": "Example: ", "feedbacks_not_completed": {"=1": " unresolved discussion", "other": " unresolved discussions"}, "idea": "Idea: ", "month": "Month", "other": "other", "others": "others", "post_deleted": "Post deleted", "post_not_found": "Post not found", "posts": "Posts", "reel": "Instagram reel", "see_posts": "See all posts", "seo": "Local SEO", "seo_post": "SEO post", "settings": "Settings", "social": "Social Media", "social_post": "Social posts", "special_hour": "Special hours", "special_hours": "Special hours", "today": "Today", "unknown_hours": "Unknown", "validate": "Validate", "week": "Week"}, "calendar_post": {"feedback_not_completed": "Unresolved discussion"}, "dashboard": "DASHBOARD"}, "date_filter": {"cancel": "Cancel", "comparison_period": {"previous_period": "Previous period", "same_period_first_year": "Same period - First year with Mal<PERSON>", "since_start": "Starting period with <PERSON><PERSON>", "title": "Compare", "unavailable": "You do not have enough history with <PERSON><PERSON> to select this option"}, "end": "End", "period": "Period", "period_tooltip": "Period", "reset": "Reset", "select_period": "Select period", "start": "Start", "validate": "Confirm"}, "date_overview": {"all": "All", "current_month": "Current month", "current_year": "Current year", "custom": "Custom", "lastDay": "Last Day", "lastWeek": "Last Week", "last_eight_months": "Last 8 months", "last_eleven_months": "Last 11 months", "last_five_months": "Last 5 months", "last_four_months": "Last 4 months", "last_month": "Last month", "last_nine_months": "Last 9 months", "last_semester": "Last semester", "last_seven_days": "Last 7 days", "last_seven_months": "Last 7 months", "last_six_months": "Last 6 months", "last_ten_months": "Last 10 months", "last_thirty_days": "Last 30 days", "last_three_months": "Last 3 months", "last_twelve_months": "Last 12 months", "last_two_months": "Last 2 months", "last_year": "Last year"}, "delete": {"cancel": "Cancel", "confirm": "Confirm", "text": "This will delete the user", "title": "Caution"}, "download_insights_footer_popin": {"title": "Preparing your download"}, "download_insights_modal": {"all_charts": "All charts", "charts_input_title": "Select the statistics you want to download", "mode_input_title": "In what format do you want your statistics ?", "title": "Download statistics", "type_input_title": "Type"}, "download_insights_summary_modal": {"download": "Download CSV", "select_period": "Confirm your Summary dates", "title": "Download a CSV Summary of all my insights"}, "duplicate_to_restaurants_dialog": {"all_businesses": "All my businesses", "error": "An error occurred during duplication", "no_business_details": "Connect the platforms to your other businesses", "no_business_title": "It seems that you have no other businesses with these platforms", "personalize_generator_locations": {"confirm_modal": {"continue_and_replace": "Continue and replace", "message": "Would you like to continue?", "title": "Be careful, you risk overwriting information already filled in"}, "subtitle": "Edit the businesses and postal code of your businesses if necessary", "success": "The generator information has been duplicated in the businesses you have selected"}, "personalize_post_data": {"story_subtitle": "Edit story scheduling", "subtitle": "Edit post scheduling"}, "restaurant_does_not_have_mapstr_premium": "The restaurant does not have a premium Mapstr account", "subtitle": "Select the businesses to which you want to duplicate your elements.", "title": "Duplicate to other businesses", "title_multiple_posts": "Duplicate as draft to other businesses"}, "edit_image": {"add_background_edition": "Add background", "add_text_edition": "Add text", "browse": "Choose from my gallery", "cancel": "Cancel", "cant_use_format": "Only the format of the first photo can be modified in a carousel", "could_not_load": "Could not load image.", "crop": "Crop", "drag_drop_file": "Drag/Drop a file", "edit_image": "Modify image", "format_other_images": "This format will be applied to other photos in a carousel", "landscape": "Landscape", "left_rotation": "Left rotation", "portrait": "Portrait", "right_rotation": "Right rotation", "rotation": "Rotation", "save": "Save", "square": "Square", "upload": "Upload a file", "vertical": "Vertical"}, "edit_media_modal": {"no_media": "No media selected", "title": "Edit your images"}, "emoji": {"categories": {"activity": "Activity", "custom": "Custom", "flags": "Flags", "foods": "Food & Drink", "nature": "Animals & Nature", "objects": "Objects", "people": "Smileys & People", "places": "Travel & Places", "recent": "Frequently used", "search": "Search Results", "symbols": "Symbols"}, "search": "Search", "skintones": {"1": "<PERSON><PERSON><PERSON>", "2": "Light", "3": "Medium-Light", "4": "Medium", "5": "Medium-Dark", "6": "Dark", "choose": "Choose default skin tone"}}, "enums": {"ai_post_settings_emoji": {"a_lot": "A lot", "none": "None", "some": "A little"}, "ai_post_settings_length": {"long": "Long (<450 characters)", "medium": "Medium (<350 characters)", "short": "Short (<250 characters)", "very_short": "Very short (<150 characters)"}, "ai_post_settings_tone": {"casual": "🤗 Friendly", "engaging": "⚡ Engaging", "formal": "ℹ️ Informative", "humoristic": "😂 Hu<PERSON>ous", "inspiring": "💡 Inspiring", "nostalgic": "🕰️ Nostalgic", "shocking": "💥 Punchy", "sophisticated": "🖋️ Sophisticated"}, "application_language": {"en": "English", "es": "Spanish", "fr": "French", "it": "Italian"}, "application_language_raw": {"en": "English", "es": "Español", "fr": "Français", "it": "Italiano"}, "call_to_action_type": {"book": "Book", "call": "Call now", "learn_more": "Learn more", "none": "None", "order": "Order online", "shop": "Buy", "sign_up": "Sign up"}, "campaign_status": {"error": "An error occurred during the sending of the campaign", "in_progress": "Campaign in progress", "not_started": "Campaign not started", "sent": "Campaign sent"}, "casl_role": {"admin": "Administrator", "editor": "Editor", "guest": "Guest", "moderator": "Moderator", "owner": "Owner"}, "chart_sort_by": {"alphabetical": "Alphabetical", "asc": "Increasing", "desc": "Decreasing"}, "countries": {"ad": "Andorra", "ae": "United Arab Emirates", "af": "Afghanistan", "ag": "Antigua and Barbuda", "ai": "<PERSON><PERSON><PERSON>", "al": "Albania", "am": "Armenia", "an": "Netherlands Antilles", "ao": "Angola", "aq": "Antarctica", "ar": "Argentina", "as": "American Samoa", "at": "Austria", "au": "Australia", "aw": "Aruba", "ax": "Åland Islands", "az": "Azerbaijan", "ba": "Bosnia and Herzegovina", "bb": "Barbados", "bd": "Bangladesh", "be": "Belgium", "bf": "Burkina Faso", "bg": "Bulgaria", "bh": "Bahrain", "bi": "Burundi", "bj": "Benin", "bl": "<PERSON>", "bm": "Bermuda", "bn": "Brunei Darussalam", "bo": "Bolivia", "br": "Brazil", "bs": "Bahamas", "bt": "Bhutan", "bw": "Botswana", "by": "Belarus", "bz": "Belize", "ca": "Canada", "cc": "Cocos Islands", "cd": "Democratic Republic of the Congo", "cf": "Central African Republic", "cg": "Congo", "ch": "Switzerland", "ci": "Ivory Coast", "ck": "Cook Islands", "cl": "Chile", "cm": "Cameroon", "cn": "China", "co": "Colombia", "cr": "Costa Rica", "cu": "Cuba", "cv": "Cape Verde", "cx": "Christmas Island", "cy": "Cyprus", "cz": "Czech Republic", "de": "Germany", "dj": "Djibouti", "dk": "Denmark", "dm": "Dominica", "do": "Dominican Republic", "dz": "Algeria", "ec": "Ecuador", "ee": "Estonia", "eg": "Egypt", "er": "Eritrea", "es": "Spain", "et": "Ethiopia", "fi": "Finland", "fj": "Fiji", "fk": "Falkland Islands", "fm": "Micronesia", "fo": "Faroe Islands", "fr": "France", "ga": "Gabon", "gb": "United Kingdom", "gd": "Grenada", "ge": "Georgia", "gf": "French Guiana", "gg": "Guernsey", "gh": "Ghana", "gi": "Gibraltar", "gl": "Greenland", "gm": "Gambia", "gn": "Guinea", "gp": "Guadeloupe", "gq": "Equatorial Guinea", "gr": "Greece", "gs": "South Georgia and the South Sandwich Islands", "gt": "Guatemala", "gu": "Guam", "gw": "Guinea-Bissau", "gy": "Guyana", "hk": "Hong Kong", "hn": "Honduras", "hr": "Croatia", "ht": "Haiti", "hu": "Hungary", "id": "Indonesia", "ie": "Ireland", "il": "Israel", "im": "Isle of Man", "in": "India", "io": "British Indian Ocean Territory", "iq": "Iraq", "ir": "Iran", "is": "Iceland", "it": "Italy", "je": "Jersey", "jm": "Jamaica", "jo": "Jordan", "jp": "Japan", "ke": "Kenya", "kg": "Kyrgyzstan", "kh": "Cambodia", "ki": "Kiribati", "km": "Comoros", "kn": "Saint Kitts and Nevis", "kp": "North Korea", "kr": "South Korea", "kw": "Kuwait", "ky": "Cayman Islands", "kz": "Kazakhstan", "la": "Laos", "lb": "Lebanon", "lc": "Saint Lucia", "li": "Liechtenstein", "lk": "Sri Lanka", "lr": "Liberia", "ls": "Lesotho", "lt": "Lithuania", "lu": "Luxembourg", "lv": "Latvia", "ly": "Libya", "ma": "Morocco", "mc": "Monaco", "md": "Moldova", "me": "Montenegro", "mf": "Saint <PERSON>", "mg": "Madagascar", "mh": "Marshall Islands", "mk": "North Macedonia", "ml": "Mali", "mm": "Myanmar", "mn": "Mongolia", "mo": "Macao", "mp": "Northern Mariana Islands", "mq": "Martinique", "mr": "Mauritania", "ms": "Montserrat", "mt": "Malta", "mu": "Mauritius", "mv": "Maldives", "mw": "Malawi", "mx": "Mexico", "my": "Malaysia", "mz": "Mozambique", "na": "Namibia", "nc": "New Caledonia", "ne": "Niger", "nf": "Norfolk Island", "ng": "Nigeria", "ni": "Nicaragua", "nl": "Netherlands", "no": "Norway", "np": "Nepal", "nr": "Nauru", "nu": "Niue", "nz": "New Zealand", "om": "Oman", "pa": "Panama", "pe": "Peru", "pf": "French Polynesia", "pg": "Papua New Guinea", "ph": "Philippines", "pk": "Pakistan", "pl": "Poland", "pm": "Saint Pierre and Miquelon", "pn": "Pitcairn", "pr": "Puerto Rico", "ps": "Palestine", "pt": "Portugal", "pw": "<PERSON><PERSON>", "py": "Paraguay", "qa": "Qatar", "re": "Réunion", "ro": "Romania", "rs": "Serbia", "ru": "Russia", "rw": "Rwanda", "sa": "Saudi Arabia", "sb": "Solomon Islands", "sc": "Seychelles", "sd": "Sudan", "se": "Sweden", "sg": "Singapore", "sh": "Saint Helena", "si": "Slovenia", "sj": "Svalbard and <PERSON>", "sk": "Slovakia", "sl": "Sierra Leone", "sm": "San Marino", "sn": "Senegal", "so": "Somalia", "sr": "Suriname", "st": "São Tomé and Príncipe", "sv": "El Salvador", "sy": "Syria", "sz": "<PERSON><PERSON><PERSON><PERSON>", "tc": "Turks and Caicos Islands", "td": "Chad", "tg": "Togo", "th": "Thailand", "tj": "Tajikistan", "tk": "Tokelau", "tl": "Timor-Leste", "tm": "Turkmenistan", "tn": "Tunisia", "to": "Tonga", "tr": "Turkey", "tt": "Trinidad and Tobago", "tv": "Tuvalu", "tw": "Taiwan", "tz": "Tanzania", "ua": "Ukraine", "ug": "Uganda", "us": "United States", "uy": "Uruguay", "uz": "Uzbekistan", "va": "Vatican City", "vc": "Saint Vincent and the Grenadines", "ve": "Venezuela", "vg": "British Virgin Islands", "vi": "United States Virgin Islands", "vn": "Vietnam", "vu": "Vanuatu", "wf": "Wallis and Futuna", "ws": "Samoa", "ye": "Yemen", "yt": "Mayotte", "za": "South Africa", "zm": "Zambia", "zw": "Zimbabwe"}, "csv_insights_chart": {"aggregated_all_followers": "Followers", "aggregated_average_reviews_ratings": "Average rating of reviews received", "aggregated_boosters_reviews_count": "Estimated reviews", "aggregated_boosters_scan_count": "Scan count", "aggregated_boosters_wheel_of_fortune_gifts_distribution": "Wheel of fortune (gifts)", "aggregated_fb_followers": "Facebook Followers", "aggregated_gmb_visibility": "Google Visibility (Appearances, Actions)", "aggregated_ig_followers": "Instagram Followers", "aggregated_platforms_ratings": "Ratings by platform", "aggregated_publications": "Publications", "aggregated_rankings": "Google positions & rankings", "aggregated_review_count": "Number of reviews received", "aggregated_reviews": "Reviews", "aggregated_semantic_analysis_by_category": "Distribution of positive sentiments by category", "aggregated_semantic_analysis_top_topics": "Semantic analysis top topics", "aggregated_stories": "Stories", "aggregated_top_search_keywords": "Top 10 keywords & searches", "all_followers": "Followers", "boosters_reviews_count": "Estimated reviews", "boosters_scan_count": "Scan count", "boosters_wheel_of_fortune_gifts_distribution": "Wheel of fortune (gifts)", "fb_followers": "Facebook Followers", "gmb_visibility": "Google Visibility (Appearances, Actions)", "ig_followers": "Instagram Followers", "insights_summary": "Summary", "keyword_search_impressions": "Search keywords", "keywords": "Keywords", "platforms_ratings": "Ratings by platform", "publications": "Publications", "reviews": "Reviews", "reviews_ratings_evolution": "Reviews by platform", "reviews_ratings_total": "Reviews split by rating", "semantic_analysis_details": "Semantic Analysis Details", "semantic_analysis_topics": "Semantic Analysis Topics", "stories": "Stories"}, "csv_insights_chart_filename": {"aggregated_all_followers": "insights_aggregated_export_social_media_kpi_global", "aggregated_average_reviews_ratings": "insights_aggregated_export_review_rating", "aggregated_boosters_reviews_count": "insights_aggregated_export_booster_reviews", "aggregated_boosters_scan_count": "insights_aggregated_export_booster_scans", "aggregated_boosters_wheel_of_fortune_gifts_distribution": "insights_aggregated_export_booster_gifts_wheel", "aggregated_fb_followers": "insights_aggregated_export_social_media_kpi_facebook", "aggregated_gmb_visibility": "insights_aggregated_export_google", "aggregated_ig_followers": "insights_aggregated_export_social_media_kpi_instagram", "aggregated_platforms_ratings": "insights_aggregated_export_platform_ratings", "aggregated_publications": "insights_aggregated_export_social_media_publications", "aggregated_rankings": "insights_aggregated_export_keywords", "aggregated_review_count": "insights_aggregated_export_review_count", "aggregated_reviews": "insights_aggregated_export_reviews", "aggregated_semantic_analysis_by_category": "insights_aggregated_export_semantic_analysis_average_by_category", "aggregated_semantic_analysis_top_topics": "insights_aggregated_export_semantic_analysis_top_topics", "aggregated_stories": "insights_aggregated_export_social_media_stories", "aggregated_top_search_keywords": "insights_aggregated_export_search_keywords", "all_followers": "insights_export_social_media_kpi_global", "boosters_reviews_count": "insights_export_booster_reviews", "boosters_scan_count": "insights_export_booster_scans", "boosters_wheel_of_fortune_gifts_distribution": "insights_export_booster_gifts_wheel", "fb_followers": "insights_export_social_media_kpi_facebook", "gmb_visibility": "insights_export_google", "ig_followers": "insights_export_social_media_kpi_instagram", "insights_summary": "summary_statistics", "keyword_search_impressions": "insighs_export_search_keywords", "keywords": "insights_export_keywords", "platforms_ratings": "insights_export_platform_ratings", "publications": "insights_export_social_media_publications", "reviews": "insights_export_reviews", "reviews_ratings_evolution": "insights_export_reviews_ratings_evolution", "reviews_ratings_total": "insights_export_reviews_ratings_total", "semantic_analysis_details": "insights_export_semantic_analysis_details", "semantic_analysis_topics": "insights_export_semantic_analysis_topics", "stories": "insights_export_social_media_stories"}, "csv_insights_field_names": {"summary": {"booster": {"booster": "<PERSON><PERSON><PERSON>", "gained_private_review_count": "Number of private reviews collected via totem", "gained_public_review_count": "Number of public reviews collected via totem", "gift_count": "Number of gifts collected (wheel)", "star": {"one": "Estimate of number of reviews collected - 1 star", "other": "Estimate of number of reviews collected - {{ starValue }} stars"}, "total_totem_scan_count": "Total totem scans", "total_wof_scan_count": "Total wheel scans", "totem": "Top {{index}} totem in review collection", "winner_count": "Number of winners (wheel)"}, "ereputation": {"e_reputation": "E-reputation", "rating": "{{platform}} rating", "reviews": {"average_rating": "Average review rating", "total_count": "Reviews received"}, "reviews_response": {"average_time": "Answer rate", "rate": "Response time"}, "sentiments_percentage": {"negative": "% of negative sentiments in reviews", "positive": "% of positive sentiments in reviews"}, "star": {"one": "Proportion of 1-star reviews", "other": "Proportion of {{ starValue }}-star reviews"}, "subject": {"flop": "Flop {{index}} subject", "top": "Top {{index}} subject"}}, "roi": {"additional_client_count": "Additional customers", "additional_sales_revenue": "Additional revenue", "performance_score": "Performance score", "roi": "Earnings", "saved_time": "Time saved"}, "seo": {"actions": {"conversion_rate": "Conversion rate", "total_actions": "Total Actions", "total_booking_click_count": "Total click booking", "total_call_count": "Total calls", "total_direction_count": "Total itinerary requests", "total_menu_click_count": "Total click menu", "total_order_click_count": "Total click order", "total_website_visit_count": "Total website visits"}, "discovery": "Keyword {{index}} discovery", "google_post_count": "Total google posts", "impressions": {"total_appearance": "Total impressions", "total_appearance_maps": "Total Maps impressions", "total_appearance_search": "Total Search impressions"}, "keyword_impression": "Keyword {{index}} appearances", "keywords_in_top20count": "# Keywords in top 20", "notoriety": "Keyword {{index}} notoriety", "seo": "SEO", "total_discovery_search_count": "Total discovery searches", "total_notoriety_search_count": "Total notoriety searches"}, "socialmedia": {"engagement_rate": "{{ platform }} engagement rate", "follower_count": "{{ platform }} followers", "impression_count": "{{ platform }}  total impressions", "post_count": "{{ platform }} total posts", "social_media": "Social Media"}}}, "customer_naming": {"firstname": "First name", "fullname": "Full name", "title_and_lastname": "Civility with last name"}, "days": {"friday": "Friday", "monday": "Monday", "saturday": "Saturday", "sunday": "Sunday", "thursday": "Thursday", "tuesday": "Tuesday", "wednesday": "Wednesday"}, "description": {"long": "<PERSON>", "short": "Short"}, "download_insights_format": {"csv": "CSV", "pdf": "PDF"}, "download_insights_type": {"precise": "Precise", "summary": "Summary"}, "e_reputation": {"aggregated_reviews": "export_aggregated_reviews", "reviews": "export_reviews", "tab_name": "E-Reputation"}, "failed_to_create_nfc_reason": {"already_exists": "An NFC with this ID already exists", "invalid_nfc_type": "Invalid NFC type", "missing_nfc_id": "Missing NFC ID", "missing_restaurant_id": "Missing restaurant ID", "missing_type_column": "The 'Type' column title is not in the correct format, change the column name and start again", "restaurant_not_found": "Restaurant not found (wrong ID)", "restaurant_without_place_id": "The restaurant has no place ID", "sticker_not_found": "The restaurant does not have a sticker"}, "file_size": {"kilooctet": "Ko", "megaoctet": "Mo", "octet": "oct"}, "gift_claim_start_date_option": {"now": "Immediately", "tomorrow": "Next visit"}, "group_access_status": {"error": "Error", "pending": "Pending", "success": "Success"}, "information_update_attribute_value": {"no": "No", "yes": "Yes"}, "information_update_platform_status_status": {"bad_access": "Error: Bad access", "done": "Done", "error": "Error: Contact Malou support", "invalid_page": "Error: Invalid page", "manual_update_error": "Error: Contact Malou support", "pending": "Pending", "unclaimed_page": "Error: Unclaimed page"}, "insights_chart": {"actions": "Actions", "aggregated_actions": "Google actions", "aggregated_apparitions": "Google Appearances", "aggregated_boosters_private_reviews_count": "Private review count", "aggregated_boosters_reviews_count": "Review count", "aggregated_boosters_scan_count": "Scan count", "aggregated_boosters_wheel_of_fortune_estimated_reviews_count": "Wheel of fortune", "aggregated_publications_table": "Publication statistics", "aggregated_rankings": "Google positions & rankings", "aggregated_review_analyses_tag_charts": "Semantic analysis of reviews", "aggregated_review_analyses_tag_evolution": "Breakdown of categories by business", "aggregated_review_ratings_kpis": "Ratings", "aggregated_reviews_count": "Number of reviews received", "aggregated_reviews_rating_average": "Average rating of reviews received", "aggregated_semantic_analysis_average_by_category": "Distribution of positive sentiments by category", "aggregated_semantic_analysis_breakdown_by_restaurant": "Breakdown of categories by business", "aggregated_semantic_analysis_chart": "Semantic analysis of reviews", "aggregated_top_posts_cards": "Top 3 posts", "aggregated_top_search_keywords": "Top 10 keywords & searches", "apparitions": "Apparitions", "boosters_private_reviews_count": "Number of private reviews (totems)", "boosters_scan_count": "Scan count", "boosters_totems_estimated_reviews_count": "Estimated number of reviews collected (totems)", "boosters_wheel_of_fortune_gifts_distribution": "Wheel of fortune (gifts)", "community": "Community", "engagement": "Engagement", "keyword_search_impressions": "User Search", "keywords": "Keywords", "post_insights": "Posts", "reel_insights": "<PERSON><PERSON>", "review_analyses_tag_charts": "Semantic analysis of reviews", "review_analyses_tag_evolution": "Semantic analysis evolution of reviews", "review_kpis": "Reviews for the selected period", "review_rating_evolution": "Reviews by platform", "review_rating_total": "Reviews split by rating", "review_ratings_kpis": "Ratings", "semantic_analysis_reviews": "Semantic analysis of reviews", "semantic_analysis_top_topics": "Top topics", "semantic_analysis_topics_evolution": "Evolution and details of your topics", "story_insights": "Stories"}, "insights_tab_filename": {"aggregated_boosters": "insights_aggregated_boosters", "aggregated_e_reputation": "insights_aggregated_e_reputation", "aggregated_e_reputation_with_new_semantic_analysis": "insights_aggregated_e_reputation", "aggregated_seo": "insight_aggregated_seo", "aggregated_seo_impressions": "insights_aggregated_google_seo", "aggregated_seo_keywords": "insights_aggregated_keywords", "aggregated_seo_keywords_v2": "insights_aggregated_keywords", "aggregated_social_networks": "insights_aggregated_social_networks", "boosters": "insights_boosters", "e_reputation": "insights_e_reputation", "e_reputation_with_new_semantic_analysis": "insights_e_reputation", "seo": "insights_seo", "seo_impressions": "insights_seo_impressions", "seo_keywords": "insights_keywords", "social_networks": "insights_social_networks", "summary": "summary_statistics", "totems": "insights_totems"}, "insights_tab_name": {"aggregated_boosters": "Aggregated boosters statistics", "aggregated_e_reputation": "Aggregated e-reputation", "aggregated_seo": "Aggregated SEO", "aggregated_social_networks": "Aggregated social networks", "boosters": "Boosters", "e_reputation": "E-reputation", "seo": "SEO", "social_networks": "Social networks"}, "keywords_popularity": {"high": "High", "low": "Low", "medium": "Medium", "pending": "Pending"}, "langs": {"en": "English", "es": "Spanish", "fr": "French", "it": "Italian"}, "mapstr_cta_button_type": {"book": "Book", "join": "Join", "menu": "<PERSON><PERSON>", "more": "Read more", "order": "Order", "see": "See"}, "media_type": {"single_image": "Image", "single_video": "Video"}, "next_draw_enabled_delay": {"after_24_hours": "After 24 hours", "after_48_hours": "After 48 hours", "after_one_week": "After one week", "always": "Unlimited", "never": "No"}, "nfc_type": {"sticker": "<PERSON>er", "totem": "Totem"}, "platform_access_status": {"bad_access": "Wrong access", "failed": "Other error", "invalid_page": "Invalid listing", "need_review": "To be reviewed", "unclaimed_page": "Unclaimed listing", "verified": "Functional"}, "platform_connection_help_feature": {"boosters": "Boosters", "comments_and_interactions": "Interactions management", "information": "Information Update", "messages": "Interactions management", "reviews": "Customer Reviews management", "seo_posts": "SEO posts management", "social_media_posts": "Social media posts management", "statistics": "Statistics"}, "platform_connection_help_information": {"address": "Address", "attributes": "Characteristics", "cover": "Cover photo", "description": "Description", "facebook_url": "Facebook page", "instagram_url": "Instagram page", "is_closed_temporarily": "Closed temporarily", "linkedin_url": "Linkedin page", "logo_url": "Profile photo", "long_description": "Long description", "main_category": "Main category", "menu_url": "Menu link", "name": "Business name", "opening_date": "Opening date", "order_url": "Order link", "other_hours": "Service hours", "phone": "Phone", "pinterest_url": "Pinterest page", "regular_hours": "Regular hours", "reservation_url": "Reservation link", "secondary_categories_names": "Secondary categories", "short_description": "Short description", "special_hours": "Special hours", "tiktok_url": "Tiktok page", "title": "Information sent when updating", "website": "Website", "x_url": "X (Twitter) page", "youtube_url": "Youtube page"}, "platform_cost": {"fees_to_claim": "Costs of $ 20 to claim the page", "free": "Free", "free_with_freemium": "Free with a freemium option to create and distribute posts", "from_109_per_month": "From 109 €/month", "from_249_per_month": "From $ 249 per month", "from_39_per_month": "From $ 39 per month", "from_499_per_month": "From $ 499 per month per location", "reservation_module": "Compulsory reservation module (commission of € 2 per reservation)", "see_with_deliveroo": "See with <PERSON><PERSON>oo", "see_with_ubereats": "See with Uber<PERSON>s"}, "platform_key": {"deliveroo": "Deliveroo", "facebook": "Facebook", "foursquare": "Foursquare", "gmb": "Google", "instagram": "Instagram", "lafourchette": "The Fork", "malouprivate": "Malou private", "manual": "Manual import", "mapstr": "Mapstr", "no_redirection": "No redirection", "opentable": "OpenTable", "pagesjaunes": "Yellow Pages", "resy": "<PERSON><PERSON>", "sevenrooms": "SevenRooms", "tiktok": "TikTok", "tripadvisor": "TripAdvisor", "ubereats": "Uber Eats", "website": "Website", "wheel_of_fortune": "Wheel of fortune", "yelp": "Yelp", "zenchef": "Zenchef"}, "platforms": {"connection": {"e_reputation": {"main_text": "E-Reputation", "sub_text": "Improve your online reputation by responding to your reviews"}, "must_have": {"main_text": "Key platforms"}, "seo": {"main_text": "SEO", "sub_text": "Work on your SEO and increase your traffic on search engines"}, "social": {"main_text": "Social networks", "sub_text": "Save time and visibility on social networks"}}}, "post_category": {"seo": "Local SEO", "social": "Social Media"}, "publication_error_code": {"connection_expired": "Your {{platform<PERSON>ey}} connection has expired. Reconnect {{platformKey}} and try again.", "unknown_error": "The publication was temporarily unavailable. Please try again.", "user_missing_appropriate_role": "You must be an administrator, editor, or moderator of your Facebook page to post. Check your Facebook permissions and try again.", "user_needs_to_log_in": "You must access Facebook and follow the instructions to post.", "user_needs_to_log_in_to_ig_app": "The Instagram account is restricted. Please log in to the Instagram app from your mobile and follow the instructions to reactivate the account, then try again."}, "publication_error_code_cta": {"connection_expired": "Reconnect {{platformKey}}", "unknown_error": "Republish now", "user_missing_appropriate_role": "See tutorial", "user_needs_to_log_in": "Access Facebook", "user_needs_to_log_in_to_ig_app": "Republish now"}, "publication_status": {"draft": "Draft", "error": "Error", "pending": "Scheduled", "published": "Published"}, "registration_time": {"few_days_deliveroo": "A few days (depending on Deliveroo)", "few_days_ubereats": "A few days (depending on ubereats)", "fifteen_minutes": "15 minutes", "five_minutes": "5 minutes", "instantly": "Instant", "sixty_minutes": "60 minutes", "ten_minutes": "10 minutes", "twenty_minutes": "20 minutes"}, "reply_tone": {"does_not_matter": "Doesn't Matter", "formal": "Formal", "informal": "Informal"}, "review_analysis_tags": {"atmosphere": "Ambiance", "expeditiousness": "Wait Time", "food": "Food", "hygiene": "Hygiene", "price": "Price", "service": "Service", "waiting": "Wait Time"}, "role": {"admin": "Administrator", "malou-basic": "Basic", "malou-free": "Free", "malou-guest": "Guest"}, "similar_restaurants_categories": {"accommodation_wellness_venue": "Accommodation and wellness venue", "asian_restaurant": "Asian restaurant", "bakery": "<PERSON><PERSON>", "bar_entertainment_venue": "Bar/entertainment venue", "bistro_pub": "Bistro/pub", "bistronomic_restaurant": "Bistronomic restaurant", "brunch_breakfast": "Brunch/breakfast", "caterer": "<PERSON><PERSON>", "coffee_tea_room": "Coffee/tea room", "creperie": "C<PERSON><PERSON>ie", "fast_food": "Fast food", "fish_seafood_restaurant": "Fish/seafood restaurant", "gastronomic_restaurant": "Gastronomic restaurant", "grocery_market": "Grocery/market", "healthy_fast_food": "Healthy fast food", "italian_restaurant": "Italian restaurant", "japanese_restaurant": "Japanese restaurant", "latino_african_caribbean_restaurant": "Latino/African/Caribbean restaurant", "occidental_restaurant": "Occidental restaurant", "oriental_mediterranean_restaurant": "Oriental/mediterranean restaurant", "other": "Other", "pizza_restaurant": "Pizza restaurant", "vegetarian_vegan_restaurant": "Vegetarian/vegan restaurant"}, "social_network_key": {"facebook": "Facebook", "instagram": "Instagram", "linkedin": "LinkedIn", "pinterest": "Pinterest", "tiktok": "TikTok", "x": "X (Twitter)", "youtube": "YouTube"}, "social_posts_list_filter": {"all": "All Posts", "draft": "Drafts", "error": "Errors", "feedback": "Comments"}, "story_publication_status": {"active": "Active", "draft": "Draft", "error": "Error", "pending": "Scheduled", "published": "Ended"}, "tiktok_privacy_status": {"follower_of_creator": "Followers", "mutual_follow_friends": "Friends", "public_to_everyone": "Public to everyone", "self_only": "Self only"}, "update_time": {"daily": "Up to 48h", "monthly": "Up to a month", "real_time": "Real Time", "weekly": "Up to one week"}, "validation_period": {"few_days": "A few days", "few_days_deliveroo": "A few days (depending on Deliveroo)", "few_days_ubereats": "A few days (depending on ubereats)", "instantly": "Instant", "one_week_with_check": "About 1 week for an advisor to finalize with you the registration of the restaurant on Thefork", "one_week_with_onboarding": "About 1 week for implementation with the onboarding team", "several_weeks": "Several weeks", "up_to_five_days": "Up to 5 days"}, "view_by": {"day": "Days", "month": "Months", "week": "Weeks"}, "wheel_of_fortune_state": {"active": "In progress", "inactive": "Inactive", "programmed": "Programmed"}}, "errors": {"profile": {"duplicate_entry": "This email address is already in use"}, "roles": {"cannot_downgrade_last_owner": "You can't remove all the owners from the restaurant."}, "social": {"account_related_ig_messaging_error": "The message permissions may not be granted to the MalouApp. \nIf so, check that your Instagram account is a business account and has at least ten followers and follows.", "no_access_to_this_page": "The provided access does not allow access to the data on this page.", "no_platform_credential": "The platform connection is not correctly configured, please reconnect it from the Settings/Platforms tab. ({{platform}})", "platform_required": "No platform selected, try changing filters or reconnecting platforms.", "request_limit_reached": "You have reached your request limit, please try again in about 15 minutes", "start_date_before_end_date": "The start date must be before the end date. \nTry changing the filters.", "time_range_too_long": "The chosen period is too wide, try with a shorter period.", "time_range_too_short": "The time range is too small, choose minimum a one day range", "timed_out": "Server error, try again."}, "unknown": "{{rawError}}", "users": {"user_has_credentials": "This user is attached to a credential. \nContact the product team to remove it."}}, "events": {"cancel": "Cancel", "change_lang": "Add a translation for the {{ lang }} version", "could_not_create": "The event could not be created", "could_not_delete": "The event could not be deleted", "create_event": "Create an event", "enter_name": "Please enter a name for the event", "error": "Error", "event_already_exists": "An event with the same name already exists on this day", "event_date": "Event date", "event_name": "Event name", "language": {"english": "english", "french": "french", "italian": "Italian", "spanish": "spanish"}, "save": "Save", "warning": "Warning"}, "feedbacks": {"admin_visibility": "Visible only to admins", "admin_visibility_v2": "Visible only to admins", "author_closed_feedback": "{{author}} marked the discussion as resolved", "author_opened_feedback": "{{author}} reopened the discussion", "close": "<PERSON> as resolved", "closed_feedback": "Marked as resolved", "could_not_create": "Comment section could not be created", "could_not_create_message": "The message could not be added", "could_not_delete": "The message could not be deleted", "could_not_edit": "The message could not be edited", "could_not_fetch": "Comments could not be recovered", "could_not_update": "Comments status could not be updated", "delete": "Delete", "edit": "Edit", "email_not_sent": "The change was taken into account but the notification email could not be sent to the other participants", "error": "Error", "feedback_and_tag": "Leave your comment here and tag them to notify them.", "feedback_not_completed": "New comment", "feedbacks": "Comments", "is_closed": "Discussion closed.", "no_feedback_yet": "You don't have any comments yet", "open": "Reopen the discussion", "opened_feedback": "Reopened the discussion", "save": "Save", "send": "Send", "set_to_close": "Mark discussion as completed", "updated": "Edited", "want_to_give_feedback": "Would you like to send a comment to your team about this post?", "write_feedback": "Send a comment", "you": "(you)"}, "filters": {"platforms": {"search_platform": "Search for platform", "validate": "Validate"}, "show_less_filters": "Show less filters", "show_more_filters": "Show more filters"}, "forbidden_error": {"error": "You don't have the rights to do this."}, "forms": {"buttons": {"cancel": "Cancel", "save": "Save"}, "text_area": {"max_length": "max characters"}}, "gallery": {"actions_header": {"search": "Search my gallery", "search_folder": "Search in {{folder}}"}, "adding_media": "Media upload in progress...", "are_you_sure": "Are you sure?", "cant_move_folders": "Folders cannot be moved", "close": "close", "combined_actions": "Bulk actions ({{number}})", "copy_of": "Copy of", "copy_to_clipboard": {"fail": "Link couldn't be copied to the clipboard", "success": "Link had been copied to the clipboard"}, "could_not_import_all": "Some media could not be imported", "crop": "Crop", "delete": "Delete", "download": "Download", "download_failed": "Download failed, please try again", "download_in_progress": "Downloading", "download_in_progress_description": "Compression of {{length}} files in .zip format", "duplicate": "Duplicate", "duplicate_here": "here ", "duplication_failed": "The duplication failed", "duplication_succeeded": "Successful duplication", "duplication_succeeded_for_restaurants": "The media have been duplicated in the venues you have selected", "edit": "Edit", "edit_image": "Edit picture", "error": "Error", "error_file_hevc": "Your file: \"{{ fileName }}\" has an encoding that is not supported by the app (HEVC), please convert it to mp4", "error_occurred": "Oops, an error has occurred", "file_size_too_big": "Your file: \"{{ fileName }}\" is too big: {{ fileSize }} MB. The maximum allowed size is {{ maxSize }} MB", "file_size_too_long": "Import error for: {{ filesNames }}. The size of a video must not exceed {{ maxSize }} seconds.", "files": "files", "finalize_import": "Finalization", "folder": {"copy_link": "Copy link to folder"}, "folder_deleted": "Folder(s) deleted.", "folder_not_deleted": "The folder could not be deleted.", "folders": {"create": "New folder", "folder_creation_modal": {"create": "Create", "folder_name": "Folder name", "same_name_error": "This name is already taken for a folder", "title": "Create a new folder"}, "folder_renaming_modal": {"folder_name": "Folder name", "same_name_error": "This name is already taken for a folder", "title": "Rename folder"}, "folders": "Folders", "gallery": "Gallery", "shared_folder_modal": {"access_to_folder_now": "Go directly to the folder", "download_media": "Download media", "media_can_be_downloaded_later": "you can always download later", "then_access_to_folder": "and access the folder", "title": "Access to the \"{{folderName}}\" folder"}}, "folders_and_media_will_be_deleted": "The selected folders and media will be deleted.", "folders_will_be_deleted": "The selected folders and their media will be deleted.", "folders_with_media_related_to_posts": "The selected folders and media contain media that are associated with one or more scheduled or draft posts. If you delete their folders, the related posts will no longer have a picture.", "import": "Uploaded", "import_in_progress": "Upload in progress", "import_media": "Upload media", "many_media_deleted": "Media deleted.", "many_media_not_deleted": "Several media could not be deleted.", "many_media_or_folders_not_deleted": "Several media or folders could not be deleted.", "media": {"media": "Media", "media_without_folder": "Folderless media"}, "media_and_folders_deleted": "Folder(s) and media(s) deleted.", "media_deleted": "Media deleted", "media_duplicated": "Your media have been duplicated in the businesses you selected", "media_move_failed": "Media move failed", "media_moved": "Your media have been moved successfully", "media_name": "Media name", "media_not_deleted": "The media could not be deleted.", "media_not_imported": "Media could not be uploaded", "media_picker": {"choose": "<PERSON><PERSON>", "choose_file": "Choose a media"}, "media_preview": {"error_occurred": "Oops, an error has occurred", "files": "files", "import_in_progress": "Upload in progress", "media_deleted": "Media deleted", "media_name": "Media name", "media_not_imported": "Media could not be uploaded"}, "media_related_to_posts": "The selected media are associated with one or more scheduled or draft posts. \nIf you delete these media, the related posts will no longer have a picture.", "media_related_to_posts_for_modification": "The selected media is associated with one or more scheduled or draft posts. \nIf you modify this media, the pictures of the related posts will also be modified.", "media_will_be_deleted": "The selected media will be deleted.", "modify": "Modify", "move": "Move towards", "move_modal_select_title": "Move file(s) towards", "move_modal_title": "Move", "must_select_items_before_delete": "Select multiple media and folders to delete them", "must_select_items_before_download": "Select multiple media and folders to download", "must_select_items_before_duplicate": "Select multiple media and folders to duplicate them", "must_select_items_before_moving": "Select at least a media to move it", "my_gallery": "My gallery", "never_posted": "Never posted", "never_posted_tooltip": "This media has never been used in a Social Media or Local SEO post from the MalouApp", "no_cancel": "No, cancel", "no_filter": "No filter.", "no_media": "Stock your media in the gallery", "no_media_caption": "Find your photo and video gallery here", "no_media_search": "Move along, nothing to see!", "no_media_search_caption": "No document matches your search", "number_of_importing_files": "{{length}} Imports in progress", "rename": "<PERSON><PERSON>", "rename_input_title": "Media name", "rename_input_title_error_message": "Please give a name to your media to validate", "rename_modal_title": "Rename media", "rotate": "Spin", "rotation": "Spin", "search_media": "Search in media library", "select": "Select (0)", "there_is_error": "Error:", "unknownError": "Unknown error", "unknown_error_upload": "Your file: \"{{ fileName }}\" could not be uploaded, please try again", "upload_error": "Sending file {{fileName}} failed: {{reason}}", "upload_errors": {"invalid_file": "Invalid file", "network_error": "Network error. Please try again"}, "upload_in_progress_description": "Importing...", "upload_media": "Import media", "yes_delete": "Yes, delete", "yes_modify": "Yes, modify"}, "generator": {"attributes": "Attributes", "audience": "Targets", "categoryList": "Categories", "city": "City", "equipment": "Equipment", "locality": "Postcode/City", "max_selected": "You can only select a maximum of {{count}} values.", "offers": "Services", "specials": "Specialties", "title": "Configure the generator", "touristics": "Nearby places"}, "generic_media_filer_uploader": {"max_medias_error": "You have reached the maximum number of media"}, "get_my_gift": {"errors": {"error_getting_gift_draw": "Error retrieving the wheel of fortune draw", "error_setting_gift_retrieved": "An error occurred when validating the recovery of your gift"}, "image_with_background_templates": {"already_claimed": {"subtitle": "It looks like you've already collected your gift 😰.", "title": "Oops!"}, "booster_pack_deactivated": {"description": "It seems that the businesses no longer has redirection.", "title": "Oops!"}, "claimable": {"claim_my_gift": {"cta": "I got my gift", "first_step": "Show this page to a member of our team and get:", "second_step": "Did you get your gift?<br/>Click on:"}, "must_be_there": {"cta": "I'm on site", "title": "To collect your gift <span style=\"color: {{color}}\">\"{{gift}}\"</span>, you must be at the business:"}}, "client_already_played": {"already_played": "It looks like you've already played.", "can_play_again_at": "You will be able to replay on {{date}}", "cant_play_again": "You cannot replay.", "title": "Oops!"}, "draw_is_expired": "Sorry your gift is no longer available", "gift_cannot_be_claimed_yet": {"retrieval_start_date": "You will be able to collect your gift on <span class=\"font-semibold\">{{date}}</span>", "title": "Sorry your gift is not yet available"}, "thanks_for_playing": {"subtitle": "We hope you liked this game. \nDon't hesitate to come back and see us for more gifts 😍.", "title": "Thanks for participating!"}, "wof_is_expired": {"come_back_later": "Sorry, our competition is over, but come back later, we do them regularly 🤗.", "title": "The game is over !"}, "wof_is_programmed": {"countdown": "D {{ days }} - {{ hours }}: {{ minutes }}", "title": "The game will start in"}}}, "hashtags": {"add": "Add", "add_category": "Add to category", "add_category_tooltip": "Add a category to this hashtag", "add_hashtags": "Add hashtags", "add_multiple_to_a_group": "Add to a category", "add_selection": "Add selection ({{selectedLength}})", "associate_category_failed": "The category could not be associated to the hashtag", "barely_used": "barely used", "begin": "Start", "category": "Category", "category_add_failed": "Failed to add category to hashtag: \"{{text}}\".", "category_creation_failed": "Failed to create category.", "category_delete_failed": "The category could not be deleted", "combined_actions": "Bulk actions ({{ number }})", "create": "Create", "create_category_failed": "The category could not be created", "delete": "Remove from the list", "delete_category": "Delete this category", "duplicated_category": "This category already exists", "duplication_failed": "Duplication failed", "duplication_succeeded": "The hashtags have been duplicated in the businesses you have selected", "edit": "Edit", "edit_brand_hashtag": "Edit brand hashtag", "edit_form": "Edit form", "edit_list": "Edit list", "error": "Error", "estimate": "Hashtag {{hashtagVolume}} - Estimated use: {{used}}", "fill_hashtag_form": "Fill in the form to generate hashtags specific to your business", "generator_form_subtitle": "This is the information that you have provided beforehand to allow us to offer you the best hashtags for your activity.", "generator_form_subtitle_no_answer": "You haven't completed the form yet. \nThis information allows us to offer you the best hashtags for your activity.", "hashtag_already_exists": "This hashtag already exists", "hashtags": "Hashtags", "hashtags_duplicated": "The hashtags have been duplicated in the businesses you have selected", "help_modal": {"calculated_score": "Every time you publish on Instagram, a hashtag score is calculated to guide you and help you use them optimally.", "consistency_post": "The consistency with the post", "follow_score": "Follow the score!", "hashtags_choice": "The choice of hashtags", "hashtags_count": "The number of hashtags", "hashtags_rotation": "The rotation of hashtags", "hashtags_score": "Hashtag score: ", "principles": "To reach this goal, you should respect a few guidelines: ", "reach_new_users": "To reach new users and grow your account, hashtags will be particularly useful", "selection_made": "We have already made a selection for you!", "use_hashtags": "How to best use your hashtags"}, "i_edit": "Edit", "i_valid": "Confirm", "incorrect_hashtag": "Incorrect hashtag", "little_used": "little used", "much_used": "much used", "my_posts": "My posts", "no_generated_hashtags": "You have not generated any hashtag yet", "no_hashtags": "#NoHashtags", "no_hashtags_subtitle": "Fill out the form to configure the automatic hashtag and keyword generator", "no_hashtags_subtitle_with_answers": "The form is ready to use, add your first hashtags", "no_hashtags_subtitle_with_filter": "No hashtag matches your search", "no_hashtags_title_with_filter": "#doesnotexist", "no_punctuation": "Punctuation is not accepted", "or": "or", "remove_from_list_confirmation": "The selected hashtag(s) will be removed.", "removed_from_list_multiple": "The hashtags have been successfully removed from your list", "removed_from_list_single": "The hashtag has been successfully removed from your list", "rename_input_error": "Please enter a hashtag name", "rename_modal_subtitle": "Brand hashtag name", "rename_modal_title": "Change brand hashtag", "search_category": "Search or create a category", "search_hashtag": "Search for a hashtag", "select": "Select (0)", "select_to_add_category": "Select multiple hashtags to add them to a category", "select_to_delete": "Select multiple hashtags to remove them from your list", "select_to_duplicate": "Select multiple hashtags to duplicate them", "selection": "Selection (0)", "specific_help": {"choose_hashtags": "How to choose hashtags?", "chosen_for_you": "We have selected for you a preliminary list of hashtags adapted to your business. You can now decide to pick up some from this list and/or add some hashtags yourself."}, "status_not_ok": "Unknown error, please try again later or contact customer service", "unknown_error": "Unknown error", "use_hashtags": "How to use hashtags?", "used": "used", "used_enough": "used enough", "validate": "Confirm", "validate_hashtag": "Confirm the brand hashtag of your business", "validation": {"add": "Add", "add_manually": "Add a hashtag manually", "add_selection": "Add ({{selectedLength}})", "already_exists": "This hashtag already exists", "at_least_one_character": "A hashtag must contain at least one character", "banned_hashtags": "The hashtag you are trying to add might be \"banned\" by Facebook or Instagram.", "choose": "<PERSON><PERSON>", "choose_for_me": "Choose for me", "close": "Close", "create": "Create", "error": "Error", "estimate": "Hashtag {{hashtagVolume}} - Estimated use: {{used}}", "from_list": "from the left list, up to 150 hashtags on which you want to appear", "hashtag": "Hashtag", "hashtag_suggestion": "Hashtag suggestions", "hashtags": "Hashtags", "hashtags_not_generated": "It seems that hashtags have not been generated...", "havent_added_hashtags": "You have not added any hashtag to your selection yet", "incorrect_hashtag": "Incorrect hashtag", "learn_more": "More", "modal": {"my_keywords": "My hashtags ({{number}})", "subtitle_right": "<b>Choose the hashtags from the list on the left</b> that you want to use to promote your business", "title_left": "Choose your hashtags", "title_right": "Your hashtags shortlist ({{number}})"}, "no_hashtags_yet": "No hashtags yet", "no_punctuation": "Punctuation is not accepted, it will be automatically removed from the hashtag", "no_whitespace": "A hashtag cannot contain spaces", "number_publication": "Number of posts", "publication_number": "Number of posts", "remove": "Remove", "removeSelection": "Remove ({{selectedLength}})", "retry": "Retry", "search_hashtag": "Search for a hashtag", "select_to_add": "Select hashtags to add to your list", "select_to_remove": "Select hashtags to remove them from your selection", "selection": "Selected (0)", "status_not_ok": "Unknown error, please try again later or contact customer service", "unknown_error": "Unknown error", "validate": "Confirm", "validate_selection": "Confirm hashtag selection", "warning": "Caution", "your_selection": "Your selection of hashtags"}, "validation_success": "Your hashtags have been successfully changed.", "volume": "Post Volume", "volumePosts": "My Posts", "which_lang": "You want to generate your hashtags in which language?"}, "hashtags_gauge": {"brandHashtag": {"maintext": "Include business hashtag"}, "hashtagsNumber": {"maintext": "Number of hashtags"}, "hashtagsVariety": {"maintext": "Variety of hashtags", "subtext": "2 or more categories <br> Vary the content of hashtags from one post to another"}, "hashtags_score": "Hashtag score:"}, "hashtags_score_gauge": {"number_of_hashtags_info_message": "Use at least {{min}} to {{max}} hashtags", "title": "Hashtag usage score"}, "header": {"admin": "Admin", "countries": {"ae": "United Arab Emirates", "be": "Belgium", "ca": "Canada", "ch": "Switzerland", "fr": "France", "gb": "United Kingdom", "lu": "Luxembourg", "ma": "Morocco", "us": "United States"}, "disconnect": "Log out", "grouped_view_link_text": "All my venues", "impersonation_mode": "Impersonation mode", "langs": {"en": "English", "es": "Spanish", "fr": "French", "it": "Italian"}, "profile": "My profile", "profile_menu": {"logout": "Sign out", "my_alerts": "My Notifications", "my_profile": "My profile", "my_reports": "My Reports"}}, "help": {"need_help": "Need help?"}, "image_transform_buttons": {"landscape": "Landscape", "original": "Original", "portrait": "Portrait", "square": "Square"}, "image_uploader": {"add_media": "Add a media", "drag_and_drop_here": "Submit your files here", "max_size": "{{size}} mo Maximum", "or_drag_and_drop": "or drag and drop your media", "processing_image": "Importing the current image ..."}, "information": {"attributes": {"add_attributes": "No attributes yet, you can add some", "attributes": "Attributes", "characteristics": "Characteristics", "duplicate_attributes": "Duplicate attributes", "error": {"gmb_credentials_error_text": "Your connection to Google is no longer valid, please reconnect the platform.", "gmb_not_connected_text": "A Google login is required to manage attributes. \nYou can reconnect it from the Platforms tab.", "gmb_not_connected_title": "Google is not connected", "no_platform_credential": "Google link is outdated. Please add the business again from home screen.", "title": "Error"}, "modal": {"number_of_attributes": "({{populated}}/{{attributes_length}} completed)", "title": "Edit my characteristics"}, "no_attributes_associated": "No attributes associated with this business", "not_informed": "Not specified", "number_of_attributes": "({{populated}}/{{attributes_length}} completed)", "number_of_attributes_short": "({{populated}}/{{attributes_length}})", "show_less": "See less", "show_more": "See the other {{ attrCount }} characteristics"}, "business_hours": {"add_hour": "Add hours", "add_schedule": "Add another schedule", "closed": "Closed", "closing": "closes at", "copy_hours_from": "Copy hours from {{ day }} to ...", "delete": "Delete", "duplicate": "Duplicate hours", "duplicate_hour": "Duplicate hours", "hours_should_be_different": "The opening and closing hours must be different", "missing_days": "Mandatory missing days in opening hours: {{days}}", "not_specified": "Not specified", "open": "Open", "opening": "opens at", "paste": "Paste", "regular_hours": "Opening hours", "remove_schedule": "Delete period", "should_not_be_empty": "Both fields must be filled in", "time_slots_warning": "Days with 3 time slots will not be updated on Facebook", "validate": "Confirm"}, "cancel": "Cancel", "continue": "Continue", "delay": "Delay", "description": {"add_description": "No description yet, you can add one", "cancel": "to cancel", "contains_phone": "To communicate a phone number on Google, use the \"phone number\" field in \"General information", "contains_url": "To communicate a link on Google, use the \"links\" fields in \"general information\"", "description": "Description", "description_too_long": "Your business description is ", "duplicate_descriptions": "Duplicate description", "edit": "Edit my description", "ignore": "Ignore", "keyword_gauge_title": "Description score", "limit_reached": "The {{ limit }} character limit has been reached for the description", "long": "<PERSON>", "modify": "Edit", "pagesjaunes": "Pages <PERSON>", "short": "Short", "to_be_updated_on": "to be updated on ", "too_long": "too long", "too_long_description": "Description too long", "validate": "Confirm"}, "detected_error": "We have detected the following error in the information provided:", "differences": "{{ totalDifferencesCount }} inconsistencies detected", "disconnected_platforms_modal": {"connect_my_platforms": "Connect my platforms", "delete_connection": "If you no longer want to connect to these platforms, delete the connection.", "description": "For security reasons, your platforms may go offline and must be reconnected regularly in order to update your information.", "description_social": "For security reasons, your platforms can be disconnected and must be regularly reconnected in order to be able to post.", "platforms_to_reconnect": "Platforms to reconnect", "title": "Your platforms have been disconnected"}, "duplication_error": "Duplication failed", "duplication_partial_error": "Not all information could be duplicated on some businesses ({{restaurantNames}}). To find out more, please contact your Malou contact or try again in the Information page for the businesses concerned.", "duplication_success": "Duplication successful", "duplication_warning_dialog_message": "Do you wish to continue ?", "duplication_warning_dialog_title": "Warning, you may overwrite information already filled in", "error": "Error", "hours": {"cancel": "Cancel", "close_temporarily": "Temporarily closed", "duplicate_other_hours": "Duplicate service schedules", "duplicate_other_hours_success": "The service times have been duplicated and sent to the referencing platforms", "duplicate_regular_hours": "Duplicate opening hours", "duplicate_regular_hours_success": "The opening hours have been duplicated and sent to the SEO platforms", "duplicate_special_hours": "Duplicate exceptional schedules", "duplicate_special_hours_success": "The exceptional schedules have been duplicated and sent to the referencing platforms", "edit_hours": "Change my business hours", "every_regular_hours_are_closed": "If you want to indicate that your restaurant is closed every day of the week, check the \"temporarily closed\" box", "exceptional": "Exceptional <i class='font-normal'>(vacations, public holidays, etc.)</i>", "facebook_warning": {"subtitle": "The platform does not accept more than two slots on the same day.", "title": "Your opening hours will not be sent to Facebook."}, "hours": "Opening hours", "invalid_dates_form_special": "You have not entered the times for an exceptional hours period.", "invalid_form_other": "The form for other schedules is not valid! \nPlease validate it to be able to save your changes", "invalid_form_regular": "The opening hours form is not valid! Please validate it to be able to register your other changes", "invalid_form_special": "You have not entered the times for an exceptional hours period.", "invalid_overlap_form_special": "Periods of exceptional schedules overlap.", "invalid_period_form_special": "You have not entered the times for an exceptional hours period.", "no_regular_hours": "You have to set regular hours before entering exceptional hours", "open": "Opening", "other": "Services", "other_hours": "Other hours", "save": "Save", "temporarily_closed_business": "You have listed your facility as temporarily closed.", "uncheck_to_edit_hours": "Uncheck the option in the modification of your schedules to reopen your business"}, "incorrect_attributes": "Incorrect(s) attribute(s)", "information": {"add_social_network_url": "Add a social network", "address": "Business location", "address_error": "Please enter a valid address. Example: 5 rue Charles de Gaulle 75010 Paris", "business_name": "Business name", "business_opening_date": "Business opening date", "cant_choose_same": "You cannot choose the same principal and secondary category", "category_already_selected": "Category already selected", "contact": "Contact", "country": "Country", "file_max_size": "The file size cannot exceed 61 MB", "file_too_large": "File too large!", "identity": "Identity", "info": "General information", "invalid_category": "Invalid category", "invalid_phone_number": "Please enter a valid phone number", "invalid_phone_number_digit": "Please enter a valid phone number", "locality": "City", "main_category": "Primary category", "max_categories": "You cannot choose more than 9 secondary categories", "menu_url": "Menu link", "opening_date": "Opening date", "order_link": "Online order link", "phone": "Phone number", "phone_prefix": "Code", "postalCode": "Postal Code ", "province": "Province", "quit_anyway": "Quit anyway?", "required_phone_number": "Mandatory field, if you want to hide it, go to your Google advanced settings", "reservation_domain_error": "Enter the link provided by your reservation service provider", "reservation_link": "Reservation link", "secondary_categories": "Additional categories", "secondary_category": "Additional category", "see_menu": "See menu", "select_address_error": "Please select an address", "social_network_urls": "Links to your social media profiles", "street": "Street", "validate": "Confirm", "website": "Website"}, "information_sent": "Information sent to", "invalid_attributes": "Some attributes are not valid for this type of business.", "linked_platforms": "Linked platforms", "other_hours": {"add_hour": "Add a service schedule", "add_other_hours": "Add other schedules", "keep_your_clients_informed": "Keep your customers informed of the details of your services during opening hours", "not_specified": "You haven't added any other hours yet"}, "platform": "Platform", "platform_comparison": {"copy_description": "Copy {{ description }} description", "differences": "{{ totalDifferencesCount }} inconsistencies detected", "dont_update_information": "Do not update this information on {{platformName}}", "error": "An error has occurred!", "field_locked": "Field locked", "field_unlocked": "Field unlocked", "information": "Information", "malou_app": "MalouApp", "send_information": "Send information", "unlock_error": "An error has occurred! This field could not be unlocked", "update_information": "Update this information on {{platformName}}", "update_platforms": "Update information on all platforms"}, "platforms_differences": "inconsistencies on listings", "saved_information": "Information saved", "see": "Show", "special_hours": {"add_hour": "Add hours", "add_period": "Add special hours", "closed": "Closed", "closing": "closes at", "delete": "Delete", "delete_hour": "Delete this slot", "display_ant_hours": "Display previous hours", "duplicate": {"error": "An error occurred during duplication.", "success": "Your exceptional schedules have been duplicated and updated on connected platforms."}, "duplication_warning_dialog_message": "Do you wish to continue ? The update will be sent automatically to platforms.", "end": "End", "hide_ant_hours": "Hide previous hours", "hide_history": "Hide schedule history", "hide_previous_hours": "Hide previous hours", "holidays": "Enter hours for days when your business has an irregular schedule (public holidays, holidays...)", "hours_should_be_different": "Opening and closing times must be different", "invalid_end_date": "The end date must be greater than the start date", "no_special_hours": "No special hours", "not_specified": "Not specified", "open": "Open", "open_history": "See schedule history", "opening": "opens at", "should_not_be_empty": "Both fields must be filled in", "show_previous_hours": "Show previous hours", "special_hours": "Special hours", "start": "Start", "validate": "Confirm"}, "status": "Status", "status_not_ok": "Unknown error, please try later or contact customer service.", "suggestions": {"accept_all": "Accept", "accept_all_changes": "Accept all changes", "information_source": "Information source", "malou_card": "Your Malou card", "pin_drop_required": "⚠️ The <strong>address</strong> must be validated directly from the Google Business Profile manager", "pin_drop_required_cta": "Confirm my address on Google", "pin_drop_required_cta_responsive": "Business profile", "reject_changes": "Reject", "store_front_required": "⚠️ Your activity category requires a physical establishment that can accommodate customers.", "suggested_changes": "Suggested Changes", "title": "Google users have suggested changes", "unknown_error": "An error has occurred, our team are doing their best to fix it, please try again later"}, "temporary_closure": {"close_if_long_period": "We recommend that you temporarily close your business listing if you are not open during seven days or more, or your business is seasonal and closes in low season.", "closed": "closed", "no_leave_it_open": "No, leave open", "open": "open", "sure_to_close": "Temporarily close your business?", "temp_closure": "Temporarily closed", "yes_close": "Yes, close"}, "the": "the", "unauthorized": "Unauthorized", "unknown_error": "Unknown error", "unknown_error_long": "An error has occurred, our team are doing their best to fix it", "update": "Update", "update_failed": "Failed updates", "update_in_progress": "Update in progress", "update_info": "Update", "update_modal": {"address_not_updated": "Address could not be updated, try to provide a complete address", "attributes_not_updated": "Attributes could not be updated", "category_not_updated": "The category could not be updated", "close": "Close", "failed": "Problem while updating", "failed_small": "Problem", "failed_to_update_properties": "One of these properties failed to update", "gmb_currently_not_allowed": "Google is having problems and the update could not be completed.", "hours_not_updated": "Facebook does not accept more than two time slots", "locked_fields_updated": "Your choices have been taken into account and will be applied for all future updates.", "not_connected_platforms": "The greyed-out platforms are not connected yet and will not be updated!", "open_info_not_updated": "Login information could not be updated", "phone_not_updated": "The phone number could not be updated", "reconnect_fb_account": "Please connect your Facebook account", "reconnect_platform": "Please reconnect the platform", "right_now": "Immediate", "saved_update": "Update saved", "status_not_ok": "Unknown error, please try again later or contact customer service", "unknown_error": "Unknown error", "up_to_a_month": "Up to 1 month", "up_to_one_week": "Up to 1 week", "up_to_three_days": "Up to 3 days", "up_to_three_weeks": "Up to 3 weeks", "up_to_two_weeks": "Up to 2 weeks", "update": "Update in one click!", "update_failed": "Update failed", "update_in_progress": "The information will take a few hours to a few days before being updated depending on the platform.", "update_time": "The update may take a few days", "updated": "Updated", "user_does_not_have_sufficient_permissions": "User does not have sufficient permissions on the Page or if the Page has double factor authentication, the user must enable double factor authentication on its account", "validate": "Confirm"}, "update_on_platforms": "Update on platforms", "update_or_continue": "You can update on all platforms or continue your changes", "update_success": "Update completed", "user_not_authorized": "You may no longer have access to this resource, try to reconnect or update your access.", "wrong_information": "Wrong information", "yes": "Yes"}, "informations": {"connect": "Connect", "contact_support": "contact Malou support", "details": "Details", "gauge": {"completed": "Well done, you have filled in all your information!", "cta": {"address": "Enter your address", "attributes": "Enter your characteristics", "category": "Indicate your main category", "default": "Fill in your information", "descriptions": "Edit my description", "descriptions_subtitle": "Please refer to our guidelines to optimize your long description and your short description.", "logo": "Add your profile photo", "name": "Indicate your name", "phone": "Enter your phone number", "regularHours": "Indicate your opening hours", "secondaryCategories": "Indicate at least 2 secondary categories", "website": "Add a link"}}, "go_to_fb_profile": "Go to my facebook page", "go_to_gmb_profile": "Go to my Google page", "infos_updates_state": "Update Status", "infos_updates_state_last_time": "Last update sent on {{ date }}", "infos_updates_state_never_sent": "You have not yet updated via MalouApp", "invalid_credentials": "The access codes you provided for this platform do not work. \nRetry the connection with valid credentials.", "invalid_login_password": "Invalid username or password", "manual_update_error": "An error occurred while updating your information. \nTo resolve it,", "missing_access": "Missing access", "missing_permissions": "Missing permissions to perform the update. \nTry the connection again.", "missing_permissions_state": "Missing permissions", "nb_failed_updates": {"=1": "{{ nbUpdates }} update error", "other": "{{ nbUpdates }} update errors"}, "nb_succeed_updates": {"=1": "{{ nbUpdates }} update sent successfully", "other": "{{ nbUpdates }} updates sent successfully"}, "need_gestion_clients_as_admin": "You have not made \"<EMAIL>\" administrator of your page. \nTry to connect again.", "not_found": "Page not found", "not_found_page": "We couldn't find your page, log back in.", "optimization": "Optimization", "page_does_not_exist_yet_details": "Your page exists but the platform has not yet validated it, the changes you make on the MalouApp will therefore not be publicly visible until the page has obtained validation from the platform.", "pending": "In progress", "pending_updates": "Your updates will be sent soon. This may take a few minutes.", "pending_updates_modal": "Your updates will be sent soon", "platform_validation_process": "Page currently being validated by the platform", "platforms": "Platforms", "see_details": "See details", "see_inconsistencies": "See my inconsistencies", "see_suggestions": "See suggestion", "special_hours": {"default_name": "Exceptional hours", "edit_name": "Edit title", "name": "Exceptional schedule name"}, "special_hours_calendar_event_validate_hours": "Validate special hours", "state": "State", "state_of_updates": "Update Status", "suggestions_details": "One of your clients noticed an inconsistency regarding your information provided on Google, here is his proposal", "tooltip_error_platform": "A problem occurred on the side of the platform {{platformName}}", "tooltip_updating_platform": "The update may take {{delay}}", "unclaimed_page": "Unclaimed page", "unclaimed_page_reconnect_platform": "We cannot update your information because you have not claimed your page. \nLog in to {{platformName}} to claim your page.", "update": {"sent": "Your updates will be sent to connected platforms", "success": "Changes saved. \nYou can now update your platforms."}, "update_done": "Changes published ", "update_error": " An error occurred while updating the platform.", "update_sent": "Changes sent", "updates_state": {"errors": {"address_edit_changes_country": "The address could not be changed. \nGet in touch with your preferred contact at Malou.", "address_missing_region_code": "The address you provided does not include a region, please modify it.", "address_removal_not_allowed": "Removing your business address is not permitted.", "ambiguous_title": "The best name is ambiguous for a language.", "attribute_cannot_be_repeated": "The \"{{field}}\" characteristic can only be specified once.", "attribute_invalid_enum_value": "Unknown value for enumeration characteristic \"{{field}}\".", "attribute_not_available": "Characteristic \"{{field}}\" not valid for this business.", "attribute_provider_url_not_allowed": "Unable to add or edit a vendor URL.", "attribute_type_not_compatible_for_category": "The \"{{field}}\" characteristic is not compatible with categories.", "blocked_region": "Due to international sanctions, Google cannot accept properties from this region.", "cannot_reopen": "Unable to reopen the business.", "error_code_unspecified": "An unknown error occurred while sending the update. \nGet in touch with your preferred contact at Malou.", "fb_field_missing_permissions": "Impossible to modify the field \"{{field}}\" because you do not have permissions as a user on your Facebook page", "fb_inconsistent_coordinates": "Impossible to update \"{{field}}\" because the coordinates of your page do not correspond to the city where it is.", "fb_login_and_follow_instructions": "You need to log in to your Facebook account and follow the instructions.", "fb_not_confirmed_user": "The user is not confirmed to perform this action", "fb_password_changed": "It seems that you have recently changed your Facebook credentials, please reconnect.", "fb_unable_download_photos": "Your photos could not be downloaded. Only photos of a size less than 4 MB and recorded in JPG, PNG, GIF, TIFF, Heif or Webp format are allowed.", "fb_user_has_not_authorized_app": "You have not authorized Malou app, please reconnect.", "fb_user_need_to_be_admin_or_moderator_or_editor": "The user account connected to the platform must be an administrator, editor, or moderator of the page in order to impersonate it. If the page business requires Two Factor Authentication, the user also needs to enable Two Factor Authentication. Please check the account’s rights on the page, or reconnect the platform to another account.", "fields_required_for_category": "The \"{{field}}\" field is required for this category of business.", "forbidden_words": "The \"{{field}}\" field contains prohibited words, please modify it.", "gmb_invalid_location_category": "Your restaurant's main category does not allow you to update {{ field }}", "gmb_platform_disabled": "The business is disabled by Google", "gmb_platform_disconnected": "The platform is disconnected, please reconnect it.", "gmb_platform_pending_verification": "The business is being verified by Google.", "gmb_platform_suspended": "The business is suspended by Google", "incompatible_more_hours_type_for_category": "Your main activity category is not compatible with this type of schedule.", "incompatible_service_area_and_category": "You cannot select the main category for a local home service business.", "invalid_address": "Invalid address. \nPlease change the address.", "invalid_area_type_for_service_area": "The service area entered is not valid.", "invalid_attribute_name": {"=1": "\"{{allFields}}\" is not available for your business type, please remove it from your information.", "other": "\"{{allFields}}\" are not available for your business type, please remove them from your information."}, "invalid_business_opening_date": "Invalid business opening date.", "invalid_category": "Invalid category identifier.", "invalid_characters": "The field \"{{field}}\" contains invalid characters, please modify it.", "invalid_hours_value": "Invalid schedule format or value.", "invalid_interchange_characters": "The field \"{{field}}\" contains invalid characters, please modify it.", "invalid_language": "The language code is invalid.", "invalid_latlng": "The location's geographic coordinates are invalid.", "invalid_phone_number": "The telephone number entered is not recognized.", "invalid_phone_number_for_region": "The phone number is not valid for this region.", "invalid_service_area_place_id": "Your Google Rental ID is invalid. \nGet in touch with your preferred contact at Malou.", "invalid_social_media_profile_url": "The social media link(s) were rejected by Google, change them to try again.", "invalid_time_schedule": "Incorrect schedule, overlap, or end time earlier than start time.", "invalid_url": "Invalid link for field \"{{field}}\". \nPlease edit the link.", "lat_lng_too_far_from_address": "The geographic coordinates and address of the business are too far apart.", "lat_lng_updates_not_permitted": "The location of the business must be entered directly in the Google information.", "link_already_exists": "The link for “{{field}}“ has already been provided.", "lodging_cannot_edit_profile_description": "Unable to edit the accommodation profile description.", "missing_address_components": "Invalid address. \nPlease change the address.", "missing_both_phone_and_website": "You must provide a phone number or website for this business.", "missing_primary_phone_number": "Primary phone number missing.", "missing_storefront_address_or_sab": "You must provide a physical address.", "opening_date_before_1ad": "The opening date cannot be earlier than the year 1 AD. \nAD", "opening_date_missing_year_or_month": "You must indicate a year or month for the opening date.", "opening_date_too_far_in_the_future": "The opening date is too far in the future. \nIt must be less than a year away.", "overlapped_special_hours": "Special schedules cannot overlap.", "phone_number_edits_not_allowed": "The phone number field has been disabled for this business, and changes are not allowed.", "pin_drop_required": "The specified address could not be found. \nPlease place a marker via the Google UI.", "po_box_in_address_not_allowed": "The address cannot contain a PO box. \nPlease change your address.", "price_currency_invalid": "The monetary currency code is invalid.", "price_currency_missing": "The monetary currency code is invalid.", "profile_description_contains_url": "The business description should not contain a URL.", "read_only_address_components": "Unable to change address.", "required_field_missing_value": "The \"{{field}}\" field is mandatory. \nPlease fill it in.", "scalable_deep_link_invalid_multiplicity": "The link for “{{field}}“ has already been provided.", "service_type_id_duplicate": "Service Type IDs are not unique within the facility.", "special_hours_set_without_regular_hours": "You can only indicate exceptional hours if the business has regular opening hours.", "stale_data": "One or more items have been recently updated by Google. \nFor the moment, only the owner of this business can modify the data located in \"{{field}}\" by accessing their Google My Business profile.", "storefront_required_for_category": "Your business category requires a physical business that can accommodate customers.", "string_too_long": "The \"{{field}}\" field is too long.", "string_too_short": "The \"{{field}}\" field is too short.", "throttled": "Unable to edit field \"{{field}}\" at this time.", "too_many_entries": "The \"{{field}}\" field has too many entries.", "too_many_updates": "Too many updates have been sent. \nA Google verification period is required.", "unverified_location": "The business has not been verified by Google."}}, "you_have_see_inconsistencies": {"=1": "⚠️ You have 1 inconsistency detected", "other": "⚠️ You have {{ nbInconsistencies }} inconsistencies detected"}, "you_have_see_inconsistencies_subtitle": "Updating your information on the platforms highlights your profile in your customers' searches.", "you_have_suggestions": "⚠️ New Google suggestion"}, "input_date_picker": {"invalid_date": "Invalid date, format MM/DD/YYYY", "invalid_max_date": "Date must be less than or equal to {{date}}", "invalid_min_date": "Date must be greater than or equal to {{ date }}"}, "inspirations": {"add_account": "Add account", "add_accounts_not_allowed": "Instagram does not allow us to add an account if you are not logged in", "add_inspirational_account": "Inspiration is the spark that turns ordinary moments into extraordinary adventures.", "bookmarked": {"add_from_social_tab": "Save posts from the New tab.", "news": "New", "no_saved_posts": "No saved posts", "saved": "Saved", "sort": "Sort"}, "connect_to_ig": "Login to Instagram to add an inspirational account", "edit": {"accounts_not_found": "No Instagram Business account found!", "add_account": "Add a new account", "add_new_account": "Add a new account", "check_spelling": "Check the spelling of the account you are looking for. It is possible that the account is not a business account, in which case we will not be able to find it.", "choose": "<PERSON><PERSON>", "followers": "followers", "inspiration_account": "Inspiring accounts", "limit_reached": "You have reached the 10 account limit, remove some in order to add new ones", "my_list": "My list", "no_available_access": "Access is no longer available. Reconnect to Instagram from the Platforms tab", "no_followed_account": "You have not followed any account yet", "no_ig_results": "Instagram did not return any result. Please try again later", "no_more_accounts": "No more accounts", "no_more_accounts_txt": "We don't have any additional inspiring accounts to display, but you can add some directly from above 👆", "our_suggestions": "Our suggestions", "posts": "posts", "problem_occurred": "Oops, an error has occurred", "remove": "Remove", "request_limit": "Instagram request limit reached, please try again in one hour", "result": "Result", "search_accounts": "You have not yet saved any inspiration accounts. Search for an account or select an account from any of our suggestions.", "search_business_account": "Search for a business account on Instagram (full account name is required)", "search_ig_account": "Search for a business account on Instagram", "see_more": "See more", "suggestions": "Suggestions", "try_to_fix": "Our team are doing their best to fix the issue", "watched_accounts": "My inspiration accounts"}, "edit_inspiring_accounts": "Edit the inspiration list", "edit_list": "Edit list", "inspiration_content": "Get inspiration for your social media content and save the best ideas here", "inspire_expire": "Inspire..Expire..Inspire..Expire..", "instagram_not_connected": "Please check if Instagram is connected, and try to reconnect it from the Platforms tab", "instagram_should_be_connected": "Instagram should be connected.", "login_ig": "Connect my Instagram account", "news": "New", "no_account_selected": "You have not selected any inspirational account yet", "no_bookmarked_posts": "You have not added any favorite posts yet", "no_bookmarked_posts_title": "Ouch! \nBlank page syndrome happens to even the best...", "platforms_not_connected": "Platform not connected", "saved": "Saved", "search_account": "Search for an account", "sort": "Sort", "try_to_fix": "Our team are doing their best to fix the issue", "users_not_found": "Some accounts were not found", "users_not_found_html": "We were unable to retrieve posts from the following accounts: {{users}}. <br><br>Check that they are <b>business accounts.</b>"}, "keywords": {"add_keywords": "Add keywords", "address": "Business location", "answer_to_form": "Reply to form", "apparitions": {"apparitions_available_soon_tooltip": "Information available from {{date}}"}, "barely_researched": "very little researched", "begin": "Start", "categories": {"customer_input": "added", "restaurant_name": "business name", "station": "station", "touristic_area": "touristic area", "venue_attribute": "attribute", "venue_audience": "audience", "venue_category": "category", "venue_equipment": "equipment", "venue_label": "label", "venue_location": "location", "venue_offer": "offer", "venue_special": "specialty", "venue_type": "type"}, "competitors": {"competitors_list": "List of competitors", "keyword": "Keyword", "local": "local", "on_keyword": "on the keyword"}, "competitors_col": "Competitors", "competitors_modal_title": "Local competitors on the keyword “{{text}}”", "config_generator": "Fill in the form to configure the keyword and hashtag generator", "confirm": "Confirm", "confirm_address": "Confirm your business location:", "contents": "Contents", "continue_and_replace": "Continue and replace", "dead_restaurant": "This business is no longer valid. Please connect to your Google account to fix the issue.", "display_list": "See list", "duplicate_error": "Duplication failed", "duplicate_other_restaurants": "Duplicate to other venues", "duplicate_success": {"=1": "The keyword has been duplicated", "other": "The keywords have been duplicated"}, "during_period": "Selected time", "edit": "Edit", "edit_form": "Edit form", "edit_list": "Edit list", "error": "Error", "error_unknown": "Unknown error", "error_unknown_text": "Our team are doing their best to fix it", "estimate": "Keyword {{keywordVolume}} - Average monthly searches: {{volume}}", "evolution": "Your business ranking on this keyword", "evolution_cell": "Ranking Evolution", "evolution_cell_keyword": "Keyword ranking evolution", "evolution_error": "No data for this period", "evolution_tooltip": "Ranking of your business for \"{{text}}\"", "fetch_volume_failed": "The recuperation of search volume has failed.", "fill_form": "Fill the form", "from_beginning": "Since the beginning", "generator": {"already_selected": "Already selected", "attributes": "Attributs", "cancel": "Annuler", "categories": "Categories", "choose_categories": "Choose a maximum of 3 categories", "choose_category": "Please choose at least one category", "choose_lang": "Choose language", "click_to_create_new": "Type the word in the search bar, and if it does not appear, click on Create", "equipment_text": "Equipment", "error": "An error occurred while generating your keywords", "establishment_attributes": "7. What attribute(s) characterize(s) your business?", "establishment_clientele": "8. What clientele do you receive and/or target in your business?", "establishment_close_places": "5. Are there popular places near your business? (4 max)", "establishment_labels": "10. Does your business have any awards ?", "establishment_services": "6. What service(s) do you offer in your business?", "establishment_type_step": "Type of business", "further_information_step": "Other information", "hashtags_not_generated": "The hashtags were not generated", "langs": "Language", "languages": "10. In which language do you want to generate your keywords?", "localisation_step": "Location", "nearby_places": "Nearby places", "neighborhoods": "Areas, streets, train stations and other attractive places nearby", "next": "Next", "no_city_found": "Can't find your city? Select your region", "physical_elements": "9. What physical element(s) characterize(s) your business?", "please_retry": "The keyword generator took too long to respond, please try to launch it again", "previous": "Previous", "quit_anyway": "Quit anyway", "restart_generator": "Try to relaunch the generator", "return": "Back", "search_attribute": "Search for an attribute", "search_category": "Search for a category", "search_equipment": "Search for equipment", "search_label": "Search for a label", "search_location": "Search for a business", "search_offer": "Search for an offer", "search_specialty": "Search for a specialty", "search_target": "Search for an audience", "select_at_least_one": "Please select at least one langage", "select_your_city": "3. Select your city", "select_your_city_subtitle": "If you can't find your city, select your region", "start_generator": "Start the generator", "target": "Targets", "unknown_error": "An error has occurred, our team are doing their best to fix it, please try again later", "validate": "Valider", "validate_zip_code": "4. Validate the postal code of your business", "which_category": "1. Which category(ies) correspond to your business? (3 max)", "which_lang": "In which language do you want to generate your keywords?", "which_specialty": "2. What are the specialty(ies) of your business? (5 max)", "wont_be_saved": "Modifications won't be saved", "wont_be_saved_text": "If you quit this page, the modifications won't be saved", "zip_code": "Postal code"}, "generator_form_subtitle": "This is the information that you have provided beforehand to allow us to offer you the best keywords for your activity.", "generator_form_subtitle_no_answer": "You haven't completed the form yet. \nThis information allows us to offer you the best Keywords for your activity.", "generator_form_title": "Information about your business", "generator_not_configured": "You have not configured the keyword generator yet", "help_modal": {"business_desc": "In your business description", "display_list": "see list", "follow_score": "Follow the score!", "gmb_posts": "In your GMB posts", "how_to_use": "How to use keywords?", "keyword_score": "Keyword score: ", "pics_name": "In your uploaded media name", "reviews_res": "In your reviews responses", "score_proposal": "A keyword score will be displayed whenever you create digital content, to help you use them effectively and consistently.", "to_improve_visibility": "To improve your presence on these keywords, you should include them as much as possible in your digital content:"}, "keyword_popularity": {"tooltip": "The keyword's popularity in the search engines of your region."}, "keywords": "Keywords", "keywords_generating_footer_popin": {"generation_in_progress": "Your keywords generation is in progress", "generation_over": "Your keywords generation is completed", "no_generation": "No keywords generation in progress"}, "keywords_generating_loader": {"generating_keywords": "Your keywords generation is in progress...", "this_may_take_several_minutes": "This may take a few minutes.", "you_can_leave_this_page": "You can leave this page, your keywords will continue to load.", "you_will_be_notified": "You will be able to follow the generation and you will receive a notification to let you know that your keywords are ready."}, "local_search": "We simulate localized searches within a radius of 2-3 km around your business.", "maps_position": "Google Maps ranking", "missing_lat_or_lng": "There seems to be a problem with your Google account on this restaurant.", "modify": "Modify", "no_competition": "No results for this keyword", "no_info": "No information", "no_information": "No information", "no_keywords": "Passkey, how many keywords do we have? \n- Zero for now 🔑", "no_keywords_selected": "You have not selected your keywords yet", "no_keywords_subtitle": "Fill out the form to configure the automatic hashtag and keyword generator", "no_keywords_subtitle_with_answers": "The form is ready to use, add your first keywords", "no_results_try_24_hours": "Google did not return any result for this search. - Please try again in 24 hours or change keyword", "no_results_try_other_period": "Google did not return any result for this search. Please try again over a different period of time.", "popularity": "Popularity", "positions": {"first": "FIRST!", "first_subtitle": "Congrats! You are first!", "other_subtitle": "This is only a delay!", "second": "{{position}}nd", "second_subtitle": "A little more and 1st place is for you!", "third": "{{position}}rd", "third_subtitle": "Congrats! You're on the podium!", "top10_subtitle": "Congrats! So close to the podium", "top15_subtitle": "Soon the top 10!", "top20": "{{position}}th", "top20_subtitle": "Back in the game! On the first page!", "under20_results_subtitle": "Maybe next time!"}, "quite_researched": "quite researched", "request": "request", "researched": "researched", "results_too_far": "Results are not relevant. Please try with another keyword.", "results_too_far_warning": "Attention: Some results are not in the correct location most likely due to temporary errors with Google.", "score": "Usage Score", "score_not_available": "The score is not available for the selected language", "search_keyword": "Search for a keyword", "search_vol": "Search volume", "see_competitors": "See the competitors", "unable_detect_location": "We are unable to detect your business location", "unknown_error": "An error has occurred, our team are doing their best to fix it, please try again later", "update_modal": {"error_occurred": "Oops, an error has occurred", "input_title": "Change search volume (by number)", "save": "Save", "title": "Volume of searches"}, "update_success": "Language updated", "use_keywords": "How to use keywords", "validation": {"add": "Add", "add_selection": "Add selection ({{selectedLength}})", "already_ten_keywords": "You already have 10 keywords in your list", "choose": "<PERSON><PERSON>", "choose_for_me": "Choose for me", "choose_keywords": "from the list on the left, up to 10 keywords with which you wish to improve your ranking", "create": "Create", "estimate": "Keyword {{keywordVolume}} - Average monthly searches: {{volume}}", "help_config": {"html_content": "<div>Limiting to <strong>10 keywords</strong>, will allow you to concentrate SEO efforts and improve your business ranking.<div>", "title": "Why choose ten keywords?"}, "keyword_already_exists": "This keyword already exists", "keywords": "Keywords", "keywords_language": "Language:", "keywords_not_added_yet": "You have not added any keywords to your selection yet", "keywords_not_generated": "It seems that the keywords were not generated...", "keywords_selection": "Your selection of keywords to improve ranking on", "keywords_suggestions": "Keyword suggestions", "lang": "Language", "no_keywords": "No keywords yet", "popularity": "Popularity", "remove": "Remove", "retry": "Try again", "search_keyword": "Search for a keyword", "select_to_add": "Select keywords to add to your list", "select_to_remove": "Select keywords to remove them from your selection", "selection_exceed_limit": "You have over 10 keywords. Please change your selection", "specific_help": {"business_comprehension": "Thanks to your answers, you have given the MalouApp a good understanding of your business and your issues", "choose_ten_keywords": "How to select 10 keywords?", "congrats": "Congratulations!", "generalists": "General", "geo_situation": "which characterizes your business location at several scales (city, district, borough, centre of interest, metro stop...)", "how_to_choose_keywords": "Which keywords should I choose?", "keywords_suggestion": "Here is now a suggestion of strategic keywords, among which you can either choose up to 10 and/or add manually. We advise you to diversify your selection with:", "large_volume": "Important search volumes, but also high competition (e.g. business)", "local": "Local", "offer": "which describe the scope of your offer (e.g. takeaway, terrace, with wifi...)", "or": "or", "position_evolution": "Follow <strong>the evolution of your business ranking</strong> on each of these keywords!", "recommendation": "*We recommend focusing on 10 keywords, until you have a good ranking, then work on new keywords in a second phase", "small_volume": "Smaller search volumes but more relevant to your offer (e.g. your specials, your business...)", "specific": "Specific", "ten_keywords": "Limiting to <strong>10 keywords</strong>, will allow you to concentrate SEO efforts and improve your business ranking.", "varied": "Varied"}, "status_not_ok": "Unknown error, please try again later or contact customer service", "unknown_error": "Unknown error", "validate": "Confirm", "validate_list": "Confirm list", "volume": "Research volume", "volume_not_found": "The average monthly searches associated with this keyword could not be found. It will be searched manually afterwards"}, "validation_success": "Your keywords have been successfully changed.", "very_researched": "very researched", "wait24h": "Please wait 24 hours before trying again", "warning_overwrite_keywords": "Be careful, you risk overwriting already filled in keywords", "warning_overwrite_keywords_message": "Would you like to continue?"}, "keywords_gauge": {"bricksNumber": {"maintext": "Use keywords"}, "bricksVariety": {"maintext": "Variety of keywords", "subtext": "(Try specialties, location or category)"}, "responseTime": {"maintext": "Respond in less than 72 hours"}, "restaurantName": {"maintext": "Include business name"}, "reviewerName": {"maintext": "Include customer name"}, "sorryWords": {"maintext": "Sorry words", "subtext": "(sorry, regret...)"}, "textLength": {"maintext": "Number of characters"}}, "keywords_score": {"choose_keywords": "Choose my keywords", "fill_keywords": "No keywords have been defined.", "hide_other_bricks": "Hide my other keywords ({{count}})", "inform_keywords": "Choose keywords", "keywords": "Keywords", "keywords_not_available": "Keywords are not available in the language of this review", "no_keyword": "You haven't defined any keywords yet! Go to the Resources > Keywords section for this.", "no_keywords": "You do not have keywords assigned to this business", "no_selected_keywords": "You have not yet chosen your keywords.", "of_keywords": "of keywords", "relevant_keywords": "Keywords most suitable for your reply", "reply_score": "Response score", "reply_tips": "Response advice", "score": "Score", "score_not_available": "The score is not available for the selected language", "show_list": "View list", "show_other_bricks": "See my other keywords ({{count}})", "tips": "Guidelines", "your_keywords": "Your keywords", "your_keywords_in_lang": "Your keywords in {{ lang }}"}, "login": {"ask_for_demo": "Request a demo", "clients": "clients", "confirm_account": {"cancel": "Cancel", "choose_password": "Choose password", "connect": "Log in", "connect_after_choosing_password": "You will be able to log in after choosing your password", "error": "Error", "expired_token": "Expired token", "invalid_token": "<PERSON><PERSON> is invalid", "link_no_longer_valid": "The link is no longer valid", "link_sent_on_email": "A link was sent to the login email", "password_copied": "Password copied", "send_new_link": "Send a new link", "sent": "<PERSON><PERSON>", "unknown_error": "Unknown error", "verified": "Your account is verified"}, "connect": "Log in", "connect_yourself": "Log in to access your personal space", "digital_for_growth": "Digital at the service of business growth", "doesnt_exit": "This combination of email password does not exist", "email": "Email", "forgot_password": {"back_to_forgot_password": "Back to reset page", "back_to_home": "Back to login page", "email": "Email", "enter_email": "Enter your email", "error": "Error", "if_has_account": "If an account is linked to this email", "info": "Info", "invalid_email": "Please enter a valid email", "link_was_sent": "An email containing a réinitialisation link has been sent to you", "no_account": "No account with this email exists", "send": "Send", "server_error": "Server error, please try again later", "will_send_link": "We will send you a link to reinitialize your password"}, "forgot_password_confirm": {"mail_send": "A reset email has been sent to you *", "to_mail": "To the address indicated: {{ email }}"}, "forgot_password_link": "Forgot your password?", "invalid_email": "Please enter a valid email", "invalid_password": "Email and password do not match", "more": "More", "new_password": {"changed_password": "Your password has been changed", "choose_password": "Choose your password", "confirm_password": "Password confirmation", "different_passwords": "Different passwords", "error": "Error", "invalid_link": "The link you used is no longer valid!", "min_length": "Minimum 8 characters", "new_password": "New Password", "password_reset_success": "Your password has been successfully reset", "password_too_short": "Your password must contain at least {{ passwordLength }} characters", "reset_password": "Reset your password", "success": "Success", "unknown_error": "Unknown error", "validate": "Validate"}, "no_account": "You do not have an account?", "no_cookies_safari": "It appears that cookies are disabled in your Safari browser. \nPlease enable them in settings to access your account.", "not_approved": "This account has not yet been approved, please check your emails", "password": "Password", "session_expired": {"text": "You will be disconnected.", "title": "Session timed out"}, "time": "time", "unknown_error": "unknown error", "visibility": "visibility", "welcome": "Welcome Back!"}, "maintenance": {"currently_down": "Our best engineers are working on it, we will be back very quickly!", "welcome": "Under maintenance"}, "malou_errors": {"cannot_delete_medias_from_other_restaurants": "You cannot delete media owned by other restaurants.", "user_cannot_update_user_restaurant": "You do not have the required rights to update this user regarding this restaurant."}, "manager": {"casl_role": "Role", "casl_role_tooltip": "Users can have different roles on different business", "email_header": "Email", "lastname": "Last name", "name_header": "First name", "pull_out": "Pull out", "restaurant_name": "Business", "restaurant_organization": "Business organizations", "user_organizations_header": "User organizations"}, "manual_import": "Manuel import", "max_number_of_media_reached": "You cannot add more than {{max}} photos or videos", "media_editor": {"error_background_saving_edition": "Error adding background to image. \nTry on chrome.", "error_text_saving_edition": "Error adding text to image. \nTry on chrome."}, "media_picker": {"error": "Unknown error", "no_photo_saved": "An unexpected error occurred, our team are trying to solve it, please try again later.", "unknown_error": "No photo saved"}, "media_picker_modal": {"all_media": "All", "choose_media": "Choose media from my gallery", "empty": "Move along, nothing to see!", "empty_sub": "No document matches your search", "error": "Unknown error", "folders": "Folders", "media": "Media", "never_posted": "Never posted", "never_posted_only": "Never posted", "never_posted_tooltip": "This media has never been used in a Social Network or SEO post from the MalouApp.", "tag": "Label", "tags": "labels", "unknown_error": "An error has occurred, our team are doing their best to fix it, please try again later"}, "media_tag_filter": {"check_all": "All", "never_posted": "Never posted", "search_tag": "Search for a label", "tags_selection": "Labels selection", "validate": "Validate"}, "media_thumbnail_slider": {"choose_cover": "Choose a cover photo", "choose_photo": "choose a photo", "error_uploading_image": "Your file could not be imported, try again in a few moments", "from": "from", "see_gallery": "my gallery"}, "media_uploader_service": {"upload_error": "Media upload failed"}, "mentions": {"actions": "Grouped Actions ({{length}})", "delete": "Delete", "error_reply": "We could not reply to this mention, please try again.", "error_snackbar": "An error has occurred.", "filters": {"tagged_in": {"comments": "Comments", "posts": "Posts"}}, "no_mentions": "No mention for the moment", "options": "Archive", "refresh": "Refresh", "replied": "Reply posted.", "tagged_in": "Tagged in"}, "messages": {"add_to_favorite": "Add to favorites", "an_error_occurred": "Oops, an error has occurred", "archive": "Archive", "attachment_not_supported": "The attachment format is not supported.", "cant_add_reaction": "Reaction could not be added", "cant_add_reaction_try_later": "Reaction could not be added, please try again later", "cant_remove_reaction": "Reaction could not be removed", "connect_platforms": "Connect platforms", "conversation_card": {"last_message_is_image": "Sent you a picture", "last_message_is_image_restaurant": "You sent a picture", "last_message_is_video": "Sent you a video", "last_message_is_video_restaurant": "You sent a video", "you": "You:"}, "conversation_panel": {"leave": "Hide conversation"}, "conversations": {"connect_platforms": "Please try reconnecting by going to the platforms tab to access your messages", "connect_platforms_button": "Connect platforms", "contact_us": "Please contact customer service.", "date": "Date", "error": "Error", "error_conversations": "An error occurred while loading <br/>your conversations", "error_loading_conversations": "An error occurred", "go_to_permissions": "Reconnect Google", "instagram_only_for_business": "Messages only available for business accounts", "no_conversations": "No messages yet", "no_conversations_for_search": "No conversation matches your search...", "no_conversations_hint": "A conversation will appear as soon as you receive a new message", "no_platforms": "You have no connected platform", "pending_agent": "Your email account is being created.", "platform": "Platform", "search_result_null": "Mayday mayday!<br>We can't see anything!", "settings": "Messaging"}, "date_format": "MM/dd/yyyy, HH:mm", "delete_enclosed_file": "Delete attachment", "error": "Error", "error_sending_message": "Try again", "error_sending_message_tooltip": "The message could not be sent, click to try again.", "errors": {"unknown_error": "An error occurred"}, "file": "File", "files_not_uploaded": "{{number}} files could not be uploaded", "files_not_uploaded_singular": "{{number}} file could not be uploaded", "filters": {"platforms": "Platforms", "read": "Read", "status": "Status", "tooltip": "Filters", "unread": "Unread"}, "form": {"placeholder": "Write your welcome message here.."}, "from_computer": "From your computer", "from_gallery": "From gallery", "from_phone": "From your phone", "image_unavailable": "The story is no longer available", "load_more_conversations": "Load more conversations", "mark_as_read": "<PERSON> as read", "mark_as_read_plural": "<PERSON> as read", "mark_as_unread": "<PERSON> as unread", "mark_as_unread_plural": "<PERSON> as unread", "max_file_size_reached": "The maximum size of each attachment is 25 MB.", "message_area": {"add_file": "Add a file", "address": "Address", "and": " and", "clientName": "Client name", "closed_day": "closed {{ closedDay }}, ", "closed_days": "closed {{ startDay }} to {{ endDay }}, ", "duplicate_template": "This template title already exists, please change it.", "menuUrl": "Menu link", "message_too_long": "The character limit has been reached.", "midnight": "midnight", "no_templates": "You don't have any Message template", "noon": "noon", "open_day": "open {{ openDay }} from {{ openTime }} to {{ closeTime }}, ", "open_day_more": "and from {{ openTime2 }} to {{ closeTime2 }}, ", "open_days": "open {{ startDay }} to {{ endDay }} from {{ openTime }} to {{ closeTime }}, ", "phone": "Phone", "placeholder": "Write your message...", "regularHours": "Opening hours", "save_as_model": "Save as template", "send": "Send", "templates": {"input_placeholder": "Give your response template a title", "input_title": "Label of the new response template", "save_send": "Save", "search": "Search for template", "subtitle": "Message template", "templates_tooltip": "Message templates", "title": "Choose a message template", "tooltip": "Use a template", "use_template": "Use this template"}, "we_are": "We are ", "website": "Website"}, "message_deleted": "Deleted a message", "message_deleted_from_you": "You deleted a message", "message_error": "The message could not be sent,", "message_has_been_deleted": "This message has been deleted", "no_cancel_action": "Cancel", "no_connected_platforms": "<PERSON><PERSON><PERSON><PERSON>, I no longer hear you,<br/>You hear me now? \nOh it happens to me all the time these days...", "notification": {"text": "You received a new message!", "title": "New message"}, "please_connect_platforms": "Please log back into the platforms tab to access your messages", "received_new_message": "New message!", "remove_from_favorites": "Remove from favorites", "setup": {"success": "Enabled messaging successfully", "success_text": "Messages may take time to appear to your clients and to be available on the MalouApp."}, "should_activate_messages": "Authorize access to messages", "story_mention": "You mentioned his name in your story", "story_mention_external": "Mentioned your name in his story", "story_reply_to": "You replied to his story", "story_reply_to_external": "Replied to your story", "sync": "Synchronize", "sync_may_take_minutes": "Message synchronization can take up to a few minutes", "tab_bar": {"archived": "Archived", "favorite": "Favorites", "general": "General"}, "templates": {"pending_templates": "Pending templates"}, "today": "Today", "unarchive": "Unarchive", "yes_disable_messaging": "Yes disable", "yes_enable_messaging": "Yes enable"}, "misc": {"confirm": "Confirm", "connect_yourself": "Let us know you better.", "error": "Error", "lastname": "Last name", "name": "First name", "welcome": "Fill your information"}, "moderation": {"all": "All", "answer": "Reply", "answer_comment_modal": {"come_back_soon": "Come back soon 😉", "deleted_comment": "The comment has been deleted by the user", "good_luck": "Good luck 🤞", "model_already_exists": "Response template already exists", "model_saved": "Response template successfully saved", "reconnect": "You are disconnected from {{platformKey}}. Please try reconnecting by going to the platforms tab", "reconnectBtn": "Reconnect", "reconnect_platform": "Credentials are no longer valid. Please reconnect from the Platforms tab", "reconnect_with_auth": "Credentials are no longer valid. Please reconnect from the Platforms tab, ensuring that all permissions are granted on your page", "reply": "Reply", "reply_comments": "Reply to comment", "reply_not_accepted": "Your message was not accepted by the platform! \nPlease edit it and try again", "reply_too_long": "Your response doesn't respect the length authorized by the platform", "save_as": "Save as a template", "send": "Send", "thank_you": "Thank you very much 🔥", "thanks": "Thanks 🍀🤞", "your_welcome": "You're welcome 😉"}, "answered": "Answered", "archive": "Archive", "archive_post_comments": "Archive all comments on this post", "comment_filter": {"show_own_comments": "Show my <br /> comments", "view_archived": "View archived"}, "comment_origin": {"comment": "Comment", "mention_in_comment": "Mention in a comment", "mention_in_post": "Mention in a post"}, "comments": {"no_comment_found_hint": "No mention or comment corresponds to your search.", "no_comment_hint": "You have no comments or mentions to view.", "no_comments": "Nothing to report, Captain!"}, "connect_platform": "Please connect at least one social media platform", "current_month": "Current month", "current_year": "Current year", "custom": "Custom", "date": "Date", "delete_label_cancel": "Cancel", "delete_label_text": "It will be permanently removed from the list of your comments templates", "delete_label_title": "Delete this template?", "delete_label_yes": "Yes, delete", "end_of_comments": "All your comments have been downloaded.", "error_no_access_to_this_page": "Please try to reconnect the platforms", "error_occurred": "Oops, an error has occurred", "error_post_view": "Post display failed", "error_synchronize_comments": "Fetching error", "filter_category": {"comment_or_mention": "Comment or mention", "options": "Options", "period": "Period", "platforms": "Platforms", "status": "Status"}, "filter_label": {"answered": "Answered", "archived": "Show archived", "comment": "Comment", "date": "Date", "mention": "Mention", "notAnswered": "Unanswered", "ownComments": "Show my comments", "platform": "Platform"}, "get_comments": "<PERSON>ad comments", "get_comments_from_connected_platforms": "Load comments from connected platforms", "last_month": "Last month", "last_year": "Last year", "no_review_change_filters": "No comments. Try changing your filters", "no_reviews": "No comments", "options": "Options", "platforms": "Platforms", "please_connect_platforms": "Please log in again in the platforms tab to access your messages and mentions", "please_connect_platforms_short": "Please log in to the platforms again", "reply": "Comment", "reply_preview": {"show_more": "See more"}, "review_in_progress": "Retrieving comments...", "see": "See", "status": "Status", "switch_view": {"by_post": "View by post", "unique": "Single view"}, "sync": "Synchronization", "teams_resolve": "Our team are doing their best to fix the issue", "title": {"comments": "Comments ", "comments_and_mentions_with_count": "Comments ({{commentsCount}}) - Mentions ({{mentionsCount}})", "mentions": " - Mentions ", "number": "({{number}})"}, "try_to_sync": "Try to synchronise or reconnect platforms", "unanswered_comments": {"=0": "See", "=1": "1 Unanswered comment", "other": "# Unanswered comments"}, "unanswered_mentions": {"=0": "See", "=1": "1 Mention not answered", "other": "# Unanswered mentions"}, "unarchive": "Unarchive", "unarchive_post_comments": "Unarchive all the comments on this post", "updated_at": "Updated on: "}, "new_free_restaurant": {"access_with_list": "You can access it from the list of businesses.", "address": "Business location", "business_name": "Business name", "error": "Error", "give_address": "Please, provide an address", "restaurant_added": "Business added!", "restaurant_already_exists": "This business already exists", "terms_of_use": "I agree to use this data only for anti Covid-19 purposes and not to retain it after 14 days", "unknown_error": "Unknown error", "validate": "Confirm"}, "new_restaurant": {"address_required": "A location is required to add an business", "back": "Back", "choose": "<PERSON><PERSON>", "choose_restaurant": {"cannot_find_latlng": "Unable to locate business", "cannot_find_latlng_details": "<PERSON><PERSON> relies on a precise location but Google does not return any information about the business. \nCheck that your Google sheet has a location, if this is already the case contact support.", "change_open_status": "Please change the opening status of the business to be able to add it.", "congrats": "Congratulations, your business has been added!", "congrats_details_1": "You can now connect the pages of your different platforms to the MalouApp.", "connect_platforms": "Connect my platforms", "error": "Error", "error_creating_restaurant": "There was an issue while creating the business", "missing_properties": "Information about the restaurant is missing.", "missing_property_list": "The following information is necessary to create the restaurant: full address, placeId.", "missing_property_list_details_link_word": "detail :", "must_choose_organization": "You must choose an organization for this business", "no_organization": "This access is not linked to an organization. Please try to reconnect to Google through the MalouApp.", "restaurant_added": "Your business has been successfully added.", "restaurant_closed": "Impossible to create a permanently closed restaurant", "restaurant_limit_reached": "Business limit reached", "not_connectable": "Not available in your subscription", "already_connected": "Already connected"}, "create": "Create", "see_page": "See page", "some_features_might_not_work_without_address": "You can't create a business until its address on Google is verified."}, "new_user": {"cancel": {"cancel": "Cancel", "confirm": "Confirm", "did_you_copy_password": "Confirm?", "did_you_copy_password_subtext": "Did you copy the password correctly:"}, "created": {"copy_password": "Please copy the following password: ", "received_connection_instructions": "The user received its connection instructions through email.", "success": "Success!"}, "error": {"invalid_email": "Invalid email", "text": "An error happened.", "user_exists": "User already exists"}, "forbidden_error": {"error": "You do not have permissions to do this action"}}, "new_version": {"text": "Refresh the page to take advantage of the latest features.", "title": "A new version is available!"}, "notification-center": {"comment_notification_item": {"multiple_restaurants_title": "{{restaurantsCount}} of your businesses received comments on social media", "single_restaurant_title": {"=1": "received a new comment", "other": "received # new comments"}}, "confirm_action_modal": {"special_hour": {"cancel": "Later", "confirm": "Confirm update", "hide": "Hidden", "show_restaurants": "See affected businesses", "text": "The following update will be automatically sent to the platforms of all your businesses:", "title": "Do you confirm this update?"}}, "hide_floating_notification": "Disable floating notifications", "hide_list": "Hide list", "information-update-error": {"title": "Error updating your information on {{restaurantName}}"}, "mark_all_as_read": "Mark all as read", "mention_notification_item": {"multiple_restaurants_title": "{{restaurantsCount}} of your businesses were mentioned on social media", "single_restaurant_title": "{{restaurantName}} has been mentioned"}, "message_notification_item": {"multiple_restaurants_title": "{{restaurantsCount}} of your businesses have received messages", "single_restaurant_desc_multiple_messages": "Respond quickly to your potential customers.", "single_restaurant_title": "{{restaurantName}} received a message!", "single_restaurant_title_multiple_messages": {"=1": "received 1 message!", "other": "received # messages!"}}, "platform-disconnected": {"description": "Log back in quickly to continue managing it", "title": "{{platformName}} has been disconnected on {{restaurantName}}", "title_multiple": "{{platformName}} has been disconnected on {{restaurantNumber}} of your businesses"}, "popins": {"post_suggestion": {"create_seo_post": "Create a Google post", "create_social_post": "Create a social media post", "event_name": "{{eventName}} is coming on {{eventDate}}!", "subtext": "Why not take the opportunity to launch a special offer?", "text": "📅 Schedule a post to tell your customers how you will celebrate"}, "special_hour": {"closed": "Closed", "open_as_usual": "Open on regular hours", "subtext": "Tell your customers if you are open to improve your position in search results.", "title": "Holiday incoming!", "update": "Update", "update_special_hours": "Edit my special hours"}}, "post-error": {"story": {"title": "An error occurred.<br>Your story could not be published on {{restaurantName}}"}, "title": "An error occurred.<br>Your post could not be published on {{restaurantName}}"}, "post-suggestion-notification": {"title": "An event is approaching: {{notificationName}} on {{notificationDate}}"}, "review-notification-item": {"negative-review-title": "Reply to this negative review on {{restaurantName}}", "new_reviews_received": "{{numberOfRestaurants}} of your businesses got new reviews", "new_reviews_received_number": {"=1": "You have a new review", "other": "You have # new reviews"}, "see_answer": "See the answer", "suggestion": "Suggestion"}, "roi_activated_notification_item": {"text": "What do you think of your earnings estimation we made?", "text_single_restaurant": "What do you think of your estimated earnings on {{restaurantName}}?", "title": "Discover the impact of your marketing efforts"}, "show_floating_notification": "Enable floating notifications", "show_list": "See list", "special-hour-notification-item": {"action_description": "You have chosen", "are_you_open": "<span>Let your customers know if you are <b>open on {{formattedDate}}</b>! (<b>{{eventName}}</b>)</span>", "are_your_restaurants_open": "<span>Let your customers know if you are<b> open on {{formattedDate}} </b>for all your businesses (<b>{{eventName}}</b>).</span>", "button": {"change_my_hours": "Change hours", "closed": "Closed", "open": "Usual hours"}, "closed_on": "Closed on {{date}}", "open_as_usual": "Usual hours on {{date}}"}, "up_to_date": "You are up to date"}, "notifications": {"at": "{{ date }} at {{ time }}", "finish_restaurant_setup": {"subtitle": "Only a few days left to fill in your mandatory information!", "title": "Evaluate your income with <PERSON><PERSON> 💰"}, "roi_settings_updated": {"subtitle": "Thank you! We will get back to you once estimated.", "title": "Evaluate your income with <PERSON><PERSON> 💰"}, "title": "Notifications"}, "openai": {"announce_event": "An upcoming event with", "announce_event_post": "The *event name* that will take place on *day*. \nThe event will include *details*", "api_error": "The generation with artificial intelligence has failed. \nTry Again", "api_rate_limit": "Usage limit reached", "contest_post": "A competition for one month for subscribers who like the post and tag a friend in the comments, with a gift", "create_error": "Error while generating via AI. \nTry Again", "generate_post": "Generate a post about", "generate_post_key": "Generate a post", "give_advice": "Generate a post about an advice ", "give_advice_key": "Give advice", "introduce_dish": "Present a dish", "introduce_dish_post": "Our new menu with", "introduce_place": "Present a place", "introduce_place_post": "The atmosphere *type of atmosphere* of our restaurant located in *business*", "max_chars": "{{maxChars}}/300 chars max.", "optimize": "To optimise", "rate_limit_reached": "You have reached the usage limit for requests to artificial intelligence", "translate": "Translate", "write_contest": "Generate a post about a contest ", "write_contest_key": "Write a contest"}, "organizations": {"confirm": "Ok, confirm", "edit": {"error": {"title": "An error has occurred"}, "limit": "Limit", "name": "Name", "restaurants_list": "Businesses", "title": "Organization preferences", "users_list": "Users"}, "will_affect_users_and_restaurants": "Deleting this organization will affect: "}, "overview": {"posts": {"seo": {"sort": {"clicked": "<PERSON>licks", "created_at": "Date", "viewed": "Views"}}}}, "page_not_found": {"do_not_exist_malou": "Just like a business with a bad rating, this page does not exist at Malou", "go_faq": "View FAQ", "go_home": "Back to home", "oh_no": "Oh no! 404", "page_not_found": "Page not found"}, "paginator": {"items_per_page": "Items per page"}, "permissions": {"and": "and", "authorization_problem": "No valid authorizations", "business_communications_missing": "Improve your experience by giving access to Google messages!", "cancel": "Cancel", "change_permissions": "Please <strong>edit </strong> settings on the relevant platform.", "connect_platform": "Connect platform", "connection_expired": "Your connection to {{platforms}} has expired.", "dont_show_message": "Do not show this message again", "edit": "Edit", "edit_params": "Edit settings", "error": "Error", "facebook_messages_missing": "Improve your experience by giving access to Facebook and Instagram messages!", "gmb_disabled": "The page is disabled. \nYou can reactivate it from the Google My Business interface.", "gmb_not_verified": "Your Google listing is not validated, certain features such as reviews or updating information may not work.", "gmb_pending_verification": "Your Google listing is being validated.", "gmb_suspended": "The business is temporarily suspended, please go to the management page from the Google my Business interface to resolve the situation.", "not_the_right_permissions": "You <strong>don't have the authorizations</strong> to access this feature.", "on": "on", "open_google_location_manager": "Request validation", "reconnect": "Reconnect", "tripadvisor_not_manager": "<NAME_EMAIL> is not the manager of the page. \nTripadvisor features may not work.", "tripadvisor_unclaimed_page": "The tripadvisor page is not claimed. \nTripadvisor features may not work.", "under_review": "Access is being verified.", "unknown_error": "An error has occurred, our team are doing their best to fix it, please try again later"}, "platforms": {"connection": {"access_to_listing": "Access my listing", "automatically_connected_platforms": "Automatically Connected SEO Platforms", "back": "Previous", "bad_access": "Incorrect access provided", "bad_access_admin": "“<EMAIL>” is not an administrator", "bad_access_credentials": "Incorrect username or password", "bad_access_reconnect_platform": "The accesses you have given us for {{platform}} are incorrect. \nCheck your access by connecting directly to {{platform}}", "cancel": "Cancel", "cannot_connect_yelp": "A problem occurred when connecting to Yelp. \nPlease contact customer support.", "change_business": "Change the selected business", "choose_restaurant": {"add_account": "Add account", "add_edit_account": "Add/Edit an account", "allow": "Allow", "allow_malou": "Allow <PERSON><PERSON> to have access to your business Facebook page (we won't publish anything without your agreement)", "business_groups": "Group of business(es)", "business_not_appear": "Your business is not listed?", "business_unavailable": "It appears that your business does not have any review on Yelp. In order to connect it, you should create a first review and try again later", "businesses_group": "2. Select a group of businesses", "cancel": "Cancel", "check_connection": "You must be logged in and be an administrator of your business Facebook page to retrieve the information", "check_connection_zenchef": "You probably misspelled your login credentials, please change your settings or use another Zenchef login account", "choose": "<PERSON><PERSON>", "choose_account": "Choose an account", "connect": "Connect", "connection": "Reconnect", "connection_other": "Connect another account", "could_not_match": "We could not retrieve the business ID. Please check the URL", "could_not_recover_info": "Error retrieving your information. \nThere is no brand page (without address) on your selected account. \nIf you don't have one, watch the <strong>tutorial to transform your page</strong>.", "create_page": "I don't have a listing yet, but I would like to create one", "duplicates_found": "Warning, we found duplicate venues in this account: ", "edit_params": "Edit settings", "error": "Error", "facebook_brand": "Facebook page linked to your brand:", "facebook_location": "Facebook page linked to your business:", "fail": "An error has occurred, our team are doing their best to fix it", "fail_data_recover": "Failed to recover information", "invalid_credentials": "Your credentials are no longer valid.", "invalid_permissions": "Invalid authorizations", "manage_business": "Manage my businesses", "media_not_supported": "Your browser does not support HTML5 videos", "missing_permissions": "Missing authorizations. Please change the settings!", "next": "Next", "no_account": "No account connected", "no_business": "Your business is not listed? You might not be manager of the business Facebook page. Go to Edit Settings to change this.", "no_business_brand": "You don't have any pages without an address linked to this Facebook account", "no_business_location": "You don't have any pages with an address linked to this Facebook account", "no_organization": "The business must be part of an organization.", "no_results": "No results found", "no_results_filter": "Oops, nothing matches your search.", "paste": "Paste here", "permissions_expires_in": "Permissions will expire in {{ days }} days", "platform_delete_error": "An error occurred while deleting the connection to the platform", "provide_link": "I have a listing, I'll give you the link", "restaurant_already_exists": "A business on the MalouApp is already associated with this GMB page. Please change the business", "retry": "Retry", "saved_option": "Saved option", "see_page": "See page", "select_account": "1. Select your account", "select_business": "3. Select your business", "stepper_title": "Check that you have completed these 3 steps before", "switch_fb_page": "Transform my facebook page", "too_many_retries": "The search failed, please try again", "try_again": "Failed to retrieve information, please try again", "unknown_error": "An error has occurred, our team are doing their best to fix it", "update_identifiers": "Oops, you haven't updated your credentials.", "valid_link": "Please enter a valid business link", "valid_permissions": "Updated authorizations", "validate": "Confirm"}, "claim_my_page": "Claim my page", "confirm": "Confirm", "connect": "Connect", "connect_mapstr_premium": "Connect my Mapstr Premium account", "connect_restaurant": "Connect my business", "connected": "Platform connected successfully", "connected_platform": "Platform successfully connected", "connected_platforms": "Connected platforms", "cover": "Cover picture", "create_business": "How to create my business on", "delete": "Delete", "delete_connection": "Delete connection", "delete_connection_mapstr": "Delete Mapstr Premium connection", "delivery": "Delivery", "disconnected_platform": "Disconnected platform", "display_config": {"html_content": "<p>By connecting all your business listings across platforms, you will have the ability to uniformize you business information in one click. </p> <p>Having inconsistencies highly impact your SEO ranking and makes you less trustworthy to your customers.</p> <br /> <p>You can easily update your information in the 'Information tab'</p>", "title": "Why connect all platforms?"}, "edit_access": "Edit credentials provided", "error": "Error", "failed": "Error", "features": {"available_features": "Available features", "update_informations": "Information Update"}, "filters": {"all": "All", "platforms": "Platforms", "to_reconnect": "To reconnect"}, "find_api_token_zenchef": "You can find your API Token in your ZenChef account settings in the Partners tab", "find_business": "Find my business", "find_restaurant": "Find my business", "informations": "Information", "informations_sent": "Information sent with each update", "invalid_page": "We were unable to update your information on \n {{platform}}, because your listing is invalid on {{platform}}.\n You will receive an email from {{platform}} when your listing is approved.", "invalid_page_v2": "The page has not been validated by {{platform}}", "malou_validation_in_progress": "Access currently being validated", "mapstr": {"step0": {"left_description": "You have paid access to Mapstr", "left_title": "I have a premium account", "right_description": "You will be connected automatically", "right_title": "You don't have a premium account"}, "step1": {"connection_key": "Connection key", "helper_text_1": "Where can I find my connection key? It can be found in the 'Publications' tab of your", "helper_text_2": "premium Mapstr account", "http_error": "Error, please try again", "malou_token": "Malou connection key", "malou_token_invalid": "The Malou connection key is invalid for this restaurant", "title": "To finalize your premium connection, add your connection key"}}, "mapstr_basic_connected": "Mapstr Basic connected", "mapstr_premium_connected": "Mapstr premium connected", "missing_permissions": "Missing credentials", "missing_permissions_ig_not_found": "Permissions to log in to Instagram are no longer valid. \nPlease re-associate your Instagram account in your Facebook account settings (\"Associated Accounts\" section).", "missing_permissions_reconnect_platform": "Permissions to connect to {{platform}} are no longer valid. \nPlease change the settings.", "modify_params": "Edit settings", "next": "Next", "no_selected_accounts": "No selected accounts", "not_connected_platform": "The platform is not connected yet", "not_connected_platforms": "Platforms not yet connected", "not_found_page": "Page not found", "page_not_found": "We could not find the page on {{platform}} platform", "page_not_valid": "invalid page", "platform_init_modal": {"congrats": "Congratulations! You have added a new business", "init_text": "<p><strong>Find</strong> your business listings on the various platforms and <strong>connect</strong> them to the MalouApp. </p><br /><p>You will then be able to update your information in one click from the <strong>'Information'</strong> tab on all connected platforms</p>", "ok": "OK"}, "platform_presentation": "{{platform}} platform presentation", "platform_unverified": "The platform is not verified", "platform_unverified_tooltip": "Please modify the connection and press \"Verify\"", "platform_update_modal": {"add_account": "Add an account", "add_account_description": "(you will be redirected to the Facebook site)", "add_account_title": "to select your business", "add_fb_account": "Add a Facebook account", "add_gmb_account_description": "(you will be redirected to the Google site)", "add_gmb_account_title": "Add a Google account", "add_manager": "You need to designate <b><EMAIL></b> as a new admin for the <b>{{ platform }}</b> page of your business", "api_token": "API Token on", "cancel": "Cancel", "choose_connection_mode": "Choose your connection mode", "connect": "Connect", "connect_manually": "Manually connect from Zenchef", "connect_page": "Connect your {{ platform}} business page to the MalouApp to update information in one click", "connect_through_fb": "Connecting to your Instagram account is done through Facebook", "connection_email": "Login email for", "credential_connection": "Login with credentials", "credentials_error": "Incorrect username/password combination. Try Again", "email": "Login email", "enter_api_token": "Manually connect your {{platform}} business page from the platform", "enter_username": "Enter your email or username / password to access your business listing on <b>{{ platform }}</b>", "error": "Error", "insufficient_permissions_zenchef": "This account does not have the permissions to sign in to a Zenchef account. Try with another account that has access to the Settings tab.", "invalid_credentials": "Provided credentials are wrong.", "invalid_email": "Please enter a valid email", "login": "Login ID/Email", "manual_connection": "Manual connection", "open_facebook": "Open Facebook", "open_gmb": "Open Google", "password": "Password", "previous": "Back", "saved_option": "Saved option", "show_tutorials": "Show tutorials", "unable_to_update": "You will not be able to update your information on <b>{{ platform }}</b> from the MalouApp.", "unknown_error": "An error has occurred, our team are doing their best to fix it, please try again later", "validate": "Confirm", "video_not_supported": "Your browser does not support HTML5 videos", "warning": "Warning"}, "presentation": "Presentation", "provide_access": "Give my credentials", "reconnect": "Reconnect", "reference_platform_incomplete": "It appears that the main listing is incomplete. Try deleting the connection and then reconnecting it after having checked that the name and location are provided", "reservation": "Reservation", "retry": "Retry", "see_details": "See details", "see_page": "See page", "select_account": "Please make sure you select at least one account in the Facebook login popup", "seo": "SEO", "social": "Social Media", "successfully_connected_platforms": {"=1": "Platform successfully connected", "other": "Platforms successfully connected"}, "uber_eats": {"errors": {"no_manager_access": "It seems that <PERSON><PERSON> is not the manager of your Uber Eats Manager page", "provide_access": "Make sure the address below is <b>\"manager\"</b> of your page"}, "open_uber_manager": "Open Uber Eats Manager", "step0": {"add_manager_address": "Add the address below as the <b>manager</b> of your page", "provide_access_to_malou": "Now <PERSON><PERSON> needs you to give him access to your Uber Eats Manager page"}, "step1": {"add_malou_as_manager": "You have <NAME_EMAIL> as a manager", "connection_success": "The connection to Uber Eats has been made successfully", "title": "2. Connect my business", "url_error": "Please enter a valid url, ex: https://merchants.ubereats.com/manager/home/<USER>", "url_placeholder": "Ex: https://merchants.ubereats.com/manager/home/<USER>", "url_title": "Paste the link of your business's Uber Eats page"}}, "unclaimed_page": "<PERSON> not claimed", "unclaimed_page_reconnect_platform": "We were unable to update your \n information on {{platform}}, because your listing is unclaimed.\n Please sign in on {{platform}} to claim your listing.", "unknown_error": "An error has occurred, our team are doing their best to fix it, please try again later", "unverified": "Unverified page", "update_information": {"address": "Address", "attributes": "Characteristics", "categories": "Categories", "category_list": "Secondary categories", "category_name": "Category", "cover": "Cover photo", "cover_url": "Cover photo", "description": "Description", "descriptions": "Description", "facebook_url": "Facebook page", "holiday_hours": "Special hours", "hours": "Hours", "instagram_url": "Instagram page", "is_closed_temporarily": "Temporarily closed", "linkedin_url": "LinkedIn page", "logo": "Logo", "logo_url": "Profile photo", "long_description": "Long description", "menu_url": "Menu link", "name": "Name", "opening_date": "Opening date", "order_link": "Online order link", "order_url": "Order link", "other_hours": "Service hours", "phone": "Phone", "pinterest_url": "Pinterest page", "profile": "Description", "regular_hours": "Opening hours", "reservation_link": "Booking link", "reservation_url": "Booking link", "secondary_categories_names": "Secondary categories", "short_description": "Short description", "social_network_urls": "Social networks", "special_hours": "Exceptional hours", "temporary_closed": "Temporarily closed", "tiktok_url": "TikTok page", "title": "Information sent with each update", "website": "Website", "x_url": "X (Twitter) page", "youtube_url": "YouTube page"}, "update_prevision_time": {"daily": "Publication can take up to 1 day", "monthly": "Publication can take up to 1 month", "real_time": "Information is published almost instantaneously", "weekly": "Publication can take up to 1 week"}, "update_time": "Update time", "validation_in_progress": "Page currently being validated by the {{ platform }} platform", "warning_delete_platform_html": "You will no longer be able to update your information or reply to your reviews on {{platform}}", "warning_delete_platform_html_mapstr": "You will no longer be able to post on Mapstr.", "warning_delete_platform_title": "<strong>Do you want to delete {{platform}} credentials on the MalouApp?<br></strong>"}, "connection_new": {"account_managed": {"foursquare": {"step_0": {"description": "<span class='{{class}}'>Foursquare</span>, a directory from the United States used by more than 30 million people, is also a social network that allows you to reference a large number of businesses, particularly in the catering industry. The data from this directory is used by Apple and Uber, which makes it all the more essential!", "primary_button": "Yes, I have a Foursquare page", "secondary_button": "No, I don't have a page", "step_name": "Do you have a Foursquare page?"}, "step_1": {"step_name": "Select your business"}, "step_2": {"description_1": "To finalize your connection, <PERSON><PERSON> needs access to your Foursquare page: ", "description_2": "Add the email address below as <span class='{{class}}'>administrator</span> of your page", "primary_button": "Open Foursquare", "step_name": "<PERSON><PERSON> as owner"}, "step_3": {"connected": "Platform connected successfully", "description_1": "Once <span class='{{class}}'>\"<EMAIL>\"</span> has been added as the owner of your page, you can confirm.", "description_2": "Our teams will take care of the rest.", "description_3": "If <EMAIL> is not added, <PERSON><PERSON> will not be able to access your resources.", "primary_button": "I confirm that I have given access to <PERSON><PERSON>", "step_name": "Confirm connection", "update_failed": "Failed to retrieve information, please try again"}, "stepper": {"subtitle": "Foursquare", "title": "Connect"}}, "resy": {"step_0": {"description": "<span class='{{class}}'>Resy</span> is an American reservation management platform that allows you to optimize the visibility of your business and collect Customer Reviews to improve your reputation. \nBy providing a centralized tool for managing reservations, Resy helps you maximize your capacity and provide a better customer experience.", "primary_button": "Yes, I have a Resy page", "secondary_button": "No, I don't have a page", "step_name": "Do you have a <PERSON><PERSON> page?"}, "step_1": {"step_name": "Select your business"}, "step_2": {"description_1": "To finalize your connection, <PERSON><PERSON> needs you to give him access to your Resy page:", "description_2": "Add the email address below as the <span class='{{class}}'>owner</span> of your page", "primary_button": "Open Resy", "step_name": "<PERSON><PERSON> as owner"}, "step_3": {"connected": "Platform successfully connected", "description_1": "Once <span class='{{class}}'>\"<EMAIL>\"</span> added as the owner of your page, you can confirm.", "description_2": "Our teams will take care of the rest.", "description_3": "If <EMAIL> is not added, <PERSON><PERSON> will not be able to access your resources.", "primary_button": "I confirm that I have given access to <PERSON><PERSON>", "step_name": "Confirm connection", "update_failed": "Failed to retrieve information, please try again"}, "stepper": {"subtitle": "<PERSON><PERSON>", "title": "Connect"}}, "tripadvisor": {"step_0": {"description": "<span class='{{class}}'>TripAdvisor</span>, the largest travel site in the world allows you to reach French and international customers. In 2022, more than 30 million reviews were left on the platform.", "primary_button": "Yes, I have a TripAdvisor page", "secondary_button": "No, I don't have a page", "step_name": "Do you have a TripAdvisor page?"}, "step_1": {"step_name": "Select your business"}, "step_2": {"description_1": "To finalize your connection, <PERSON><PERSON> needs access to your TripAdvisor page: ", "description_2": "Add the email address below as the <span class='{{class}}'>owner</span> of your page", "primary_button": "Open TripAdvisor", "step_name": "<PERSON><PERSON> as owner"}, "step_3": {"connected": "Platform connected successfully", "description_1": "Once <span class='{{class}}'>\"<EMAIL>\"</span> has been added as the owner of your page, you can confirm.", "description_2": "Our team will take care of the rest.", "description_3": "If <EMAIL> is not added, <PERSON><PERSON> will not be able to access your resources.", "primary_button": "I confirm that I have given access to <PERSON><PERSON>", "step_name": "Confirm connection", "update_failed": "Failed to retrieve information, please try again"}, "stepper": {"subtitle": "Tripadvisor", "title": "Connect"}}, "yelp": {"step_0": {"description": "<span class='{{class}}'>Yelp</span> is a crowdsourced local business review and social networking site. This popular American business review site is now a major player in France.", "primary_button": "Yes, I have a Yelp page", "secondary_button": "No, I don't have a page", "step_name": "Do you have a Yelp page?"}, "step_1": {"step_name": "Select your business"}, "step_2": {"description_1": "To finalize your connection, <PERSON><PERSON> needs access to your Yelp page: ", "description_2": "Add the email address below as <span class='{{class}}'>administrator</span> of your page", "primary_button": "Open Yelp", "step_name": "<PERSON><PERSON> as owner"}, "step_3": {"connected": "Platform connected successfully", "description_1": "Once <span class='{{class}}'>\"<EMAIL>\"</span> has been added as the owner of your page, you can confirm.", "description_2": "Our team will take care of the rest.", "description_3": "If <EMAIL> is not added, <PERSON><PERSON> will not be able to access your resources.", "primary_button": "I confirm that I have given access to <PERSON><PERSON>", "step_name": "Confirm connection", "update_failed": "Failed to retrieve information, please try again"}, "stepper": {"subtitle": "Yelp", "title": "Connect"}}}, "deliveroo": {"step_0": {"description": "Enter your email and password to access your business page on Deliveroo", "email_address": "Email address", "invalid_email": "Please enter a valid email", "password": "Password", "step_name": "Log in"}, "step_1": {"step_name": "Select your business"}, "stepper": {"subtitle": "Deliveroo", "title": "Connect"}}, "facebook_instagram": {"step_0": {"description": "Instagram and Facebook are two platforms of the Meta group, so the two platforms will be connected at the same time via Facebook.", "primary_button": "Yes, I have an Instagram account", "secondary_button": "No, show me how to create an Instagram account", "step_name": "Do you have an Instagram page?", "title": "Do you have an Instagram account for your business?"}, "step_1": {"description_1": "<span class='{{class}}'>Download</span> the Instagram app from the App Store or Google Play Store or go to ", "description_1_url": "https://www.instagram.com/", "description_2": "Click <span class='{{class}}'>“Register”</span>. Enter your email address or phone number and create a strong password.", "description_3": "Choose a username that clearly represents your business, such as its name.", "description_4": "Add a profile photo, bio and a link to your website.", "step_name": "Create an Instagram page", "title": "Create an Instagram account for your business"}, "step_2": {"description": "To use Mal<PERSON>, you must have a Meta Business account, consisting of a Facebook page linked to an Instagram account", "primary_button": "Yes, I have a Facebook page", "secondary_button": "No, show me how to create a Facebook page", "step_name": "Do you have a Facebook page?", "title": "Do you have a Facebook page for your business?"}, "step_3": {"description_1": "Go to", "description_1_url": "https://www.facebook.com/pages/create", "description_2": "Enter a Page name and category. You can also add a bio to your Page.", "description_3": "Click on “Create Page”.", "description_4": "If you want to personalize your <PERSON>, you can add your bio, a profile photo and a cover photo.", "description_5": "Click Done.", "description_main": "https://www.facebook.com/help/***************", "primary_button": "Yes, I have a Facebook page", "secondary_button": "No, show me how to create a Facebook page", "step_name": "Create a Facebook page for your business", "title": "Create a Facebook page for your business"}, "step_4": {"description": "Go to", "linked_page": "Linked page", "not_linked_page": "Unlinked page", "primary_button": "Yes", "primary_button_sub": "(you will be redirected to Facebook)", "primary_button_sub_please_wait": "Redirection in progress...", "secondary_button": "No, show me how to do it", "step_name": "Is your page linked to your Instagram account?", "title": "Is your page linked to your Instagram account?"}, "step_5": {"description_1": "Log in to Facebook, then navigate to the Page you want", "description_1_url": "associate with your Instagram account", "description_2": "From your Page, click \"<span class='{{class}}'>Manage</span>\"", "description_3": "Under Business Dashboard, click \"<span class='{{class}}'>Associated Accounts</span>\"", "description_4": "Click \"<span class='{{class}}'>associate account</span>\"", "primary_button": "Next", "primary_button_sub": "(you will be redirected to Facebook)", "step_name": "Link your Instagram page to your Facebook page", "title": "Link your Instagram page to your Facebook page"}, "step_6": {"facebook": {"step_name": "Select your Facebook account"}, "instagram": {"step_name": "Select your Instagram account"}}, "stepper": {"title": "Connect Instagram and Facebook"}}, "google": {"step_0": {"step_name": "Log in"}, "step_1": {"step_name": "Select your business"}, "stepper": {"subtitle": "Google", "title": "Connect"}}, "mapstr": {"step_0": {"malou_token_created": {"description": "Go to <span class='{{class}}'> the bottom of your Publications page</span> to <span class='{{class}}'>copy your Malou connection key</span>.", "primary_button": "Open Mapstr"}, "malou_token_not_created": {"description": "The Mapstr teams are creating your key.", "estimated_date": "Estimated creation date: ", "primary_button": "Send me a reminder", "reminder_sent": "An automatic email will be sent when the Mal<PERSON> token is ready", "title": "Creating"}, "step_name": "Copy your \"Malou connection key\""}, "step_1": {"api_token": "Malou connection key", "http_error": "Error, please try again", "malou_token_invalid": "The Malou connection key is invalid for this restaurant", "step_name": "Paste your \"Malou connection key\""}, "stepper": {"subtitle": "Mapstr", "title": "Connect"}}, "parent_stepper": {"stepper": {"contact": "Contact support"}}, "password_managed": {"lafourchette": {"step_0": {"description": "<span class='{{class}}'>The Fork</span> (formerly LaFourchette) is a French online restaurant reservation platform, present in more than twenty countries in Europe and Latin America .", "primary_button": "Yes, I have a The Fork page", "secondary_button": "No, I don't have a page", "step_name": "Do you have a The Fork page?"}, "step_1": {"step_name": "Select your business"}, "step_2": {"connected": "Platform connected successfully", "description": "Indicate your email or username/password allowing access to your business page on The Fork", "invalid_email": "Please enter a valid email", "login": "Identifier", "password": "Password", "primary_button": "Log in", "step_name": "Log in", "update_failed": "Failed to retrieve information, please try again"}, "stepper": {"subtitle": "The Fork", "title": "Connect"}}, "pagesjaunes": {"step_0": {"description": "French historical directory, well known for easily finding a telephone number, the <span class='{{class}}'>Yellow Pages</span> play a fairly important role in Google referencing. It' is a data source widely taken into account at this level and always consulted. Management of an business page is done from the Solocal site.", "primary_button": "Yes, I have a Yellow Pages page", "secondary_button": "No, I don't have a page", "step_name": "Do you have a Yellow Pages page?"}, "step_1": {"step_name": "Select your business"}, "step_2": {"connected": "Platform connected successfully", "description": "Indicate your email or username/password allowing access to your business page on Yellow Pages", "invalid_email": "Please enter a valid email", "login": "Identifier", "password": "Password", "primary_button": "Log in", "step_name": "Log in", "update_failed": "Failed to retrieve information, please try again"}, "stepper": {"subtitle": "Yellow Pages", "title": "Connect"}}}, "sevenrooms": {"step_0": {"description": "Enter your email and password to access your page on SevenRooms", "email_address": "Email address", "invalid_email": "Please enter a valid email", "password": "Password", "step_name": "Log in"}, "step_1": {"step_name": "Select your business"}, "stepper": {"subtitle": "SevenRooms", "title": "Connect"}}, "shared": {"access_information": {"missing_permissions": "Missing permissions. Please change settings", "permissions_expire_in": "Permissions will expire in {{ days }} days", "valid_permissions": "Up to date permissions"}, "business_selector": {"duplicates_found": "Attention, we found duplicate businesses on this account", "no_results_title": "No pages found", "see_page": "View page"}, "see_tutorial": "See the tutorial", "steps": {"select_business": {"no_results_description": "Please enter your page link below", "paste_your_link": "Don't see your business? Paste the link to your page", "select_business": "Select your business"}, "select_credential_and_business": {"connection_error": "Error trying to connect the platform", "connection_success": "Platform connected successfully", "credentials_search_error_description": "Please try to connect an account with the previous steps", "credentials_search_error_title": "No accounts found", "no_results_description": "Please check that the account is linked to an business", "platforms_search_error_description": "Please reconnect the account", "platforms_search_error_title": "Unable to retrieve businesses", "select_business": "2. Select your business", "select_credential": "1. Select your account"}}}, "simple_account_managed": {"opentable": {"step_0": {"description_1": "To finalize your connection, <PERSON><PERSON> needs access to your OpenTable page", "description_2": "Add the address below as your page user with <span class='{{class}}'>all access</span>", "primary_button": "Open OpenTable Guest Center", "step_name": "<PERSON><PERSON> as manager"}, "step_1": {"connected": "Platform successfully connected", "connection_success": "Connection to OpenTable was successful", "description": "Paste the link to your property's OpenTable Guest Center page", "no_results_description": "Please check the link", "no_results_title": "No result", "step_name": "Paste your page link", "update_failed": "Failed to retrieve information, please try again"}, "stepper": {"subtitle": "OpenTable", "title": "Connect"}}, "ubereats": {"step_0": {"description_1": "<PERSON><PERSON> needs you to give him access to your Uber Eats Manager page", "description_2": "Add the address below as the <span class='{{class}}'>\"Manager\"</span> of your page. To do this, go to the <span class='{{class}}'>Users page</span> of your account.", "primary_button": "Open Uber Eats Manager", "step_name": "<PERSON><PERSON> as manager"}, "step_1": {"connected": "Platform connected successfully", "connection_success": "The connection to Uber Eats was successful", "description": "Paste your business's Uber Eats Manager page link", "no_results_description": "Please check the link", "no_results_title": "No results", "step_name": "Paste your page link", "update_failed": "Failed to retrieve information, please try again"}, "stepper": {"subtitle": "UberEats", "title": "Connect"}}}, "tiktok": {"step_0": {"cta": "Continue with TikTok", "step_name": "Scan this QR code with your phone or click on the link to be redirected to TikTok"}, "stepper": {"subtitle": "TikTok", "title": "Connect"}}, "zenchef": {"step_0": {"description_1": "<span class='{{class}}'>1.</span> Go to the <span class='{{class}}'>Settings > Integration</span> page to <span class='{{class}}'>copy your Restaurant ID.</span >", "description_2": "<span class='{{class}}'>2.</span> Scroll down on the <span class='{{class}}'>Integration</span> page until you find <PERSON><PERSON> in the list and click on \"Connect\".", "open_zenchef": "Open Zenchef", "step_name": "Copy your Zenchef \"Restaurant ID\""}, "step_1": {"connection_success": "Platform connected successfully", "restaurant_id": "Restaurant ID", "restaurant_not_found": "The restaurant was not found, please make sure you have connected <PERSON><PERSON> in your Zenchef interface", "step_name": "Paste your Zenchef \"Restaurant ID\""}, "step_2": {"step_name": "Select your business"}, "stepper": {"subtitle": "Zenchef", "title": "Connect"}}}, "help": {"config_time": "Setup time", "creation_time": "Creation time", "descriptions": {"deliveroo": "Deliveroo is a meal delivery platform created in 2013 that has established itself among the delivery players in France and worldwide. Each month, an average of six million people order from 115,000 restaurants via Deliveroo.", "facebook": "The social network, used by more than half of the French population, is also an excellent showcase for your business, allowing internet users to find information about your venue.", "foursquare": "The directory from the United States, used by more than 30 million people, is also a social network that allows referencing a large number of businesses, especially in the restaurant industry. Apple and Uber use data from this directory, making it all the more indispensable!", "gmb": "This is the essential platform for your business. Your business profile will be visible in Google searches and on Google Maps.", "instagram": "One of the most used social networks in the world by individuals and businesses. You can create a page for your business to showcase your dishes or products. Instagram will allow you to communicate with your customers and strengthen your ties with them.", "lafourchette": "TheFork (formerly LaFourchette) is a French online restaurant reservation platform, present in more than twenty countries in Europe and Latin America.", "mapstr": "Mapstr is an app, social network, and mapping service that allows you to save your favorite places to share with friends.", "opentable": "OpenTable is an American reservations management platform that allows you to optimize the visibility of your business and collect Customer Reviews to improve your reputation. \nBy providing a centralized tool for managing reservations, OpenTable helps you maximize your seating capacity and provide a better guest experience.", "pagesjaunes": "The historical French directory, well-known for easily finding a phone number, plays a significant role in Google referencing. It is a widely considered data source and always consulted. The management of a business page is done through a site called Solocal.", "resy": "Resy is an American reservations management platform that allows you to optimize the visibility of your business and collect Customer Reviews to improve your reputation. \nBy providing a centralized tool for managing reservations, Resy helps you maximize your capacity and provide a better customer experience.", "sevenrooms": "SevenRooms is a customer experience management platform specially designed for hotel, catering and leisure. \nIt allows establishments to manage reservations, relational marketing, loyalty, and to analyze customer behavior to offer personalized service.", "tripadvisor": "The world's largest travel site allows you to reach both French and international customers. In 2022, over 30 million reviews were left on the platform.", "ubereats": "Uber Eats is a meal delivery service launched by Uber in 2015, which has established itself among the delivery players in France and worldwide. In 2022, Uber Eats is present in nearly 400 cities in mainland France and overseas.", "yelp": "Yelp is a participatory site for reviews of local businesses and social networking. The popular American review site is now a major player in France.", "zenchef": "Zenchef helps you optimize the management of your restaurant by supporting you before, during, and after service. From online reservations to customer loyalty and table payments, Zenchef has already been adopted by over 6,000 restaurants in France and Europe."}, "information": "Information", "information_update": "Updated information", "linked_platforms": "Linked platforms", "price": "Price", "show_tutorials": "Show tutorials", "title": {"connected": "Presentation of ", "not_connected": "About "}, "why_signin": "Why create a listing?"}, "navbar": {"connection": "CONNECTION", "help": "HELP"}, "scraping_based": {"account_managed_step_1": "Go to your page:", "account_managed_step_2": "Add “{{malouManagerEmail}}” as a new administrator of your business {{platform}} page", "account_managed_title": "<PERSON><PERSON> needs you to give it access to your {{platform}} page:", "cancel_modal_message": "You will not be able to update your {{platform}} information from the MalouApp", "confirm_access": "I confirm that I have given access to <PERSON><PERSON>", "credentials_managed_title": "Enter your email or username/password to access your business page on {{platform}}", "enter_link_directly": "Enter the link of your {{platform}} page directly below", "finalize_connection": "To finalize, <PERSON><PERSON> needs you to give him access to your TripAdvisor page:", "give_credentials": "Give my credentials", "give_malou_access": "Give access to Malou", "i_dont_have_a_page": "I don't have a page", "i_have_a_page": "I have a page", "i_want_to_connect_it": "I want to connect it", "i_want_to_create_one": "I want to create one", "login": "Username", "open_platform": "Open {{platform}}", "our_teams_will_take_care_of_the_rest": "Our teams will take care of the rest", "password": "Password", "provide_access": "Add the address below as the <b>administrator</b> of your page", "provide_admin_access": "Once <strong>\"{{ malouManagerEmail }}\"</strong> is added as an administrator, you can confirm.", "url_error": "Please enter a valid url, ex: {{url}}", "url_placeholder": "Ex: {{url}}", "url_title": "Don't see your business? Paste the link to it", "verify_after_adding_admin": "Once added, come back to this page and click on “verify”, our teams will take care of the rest.", "view_complete_tutorial": "View full tutorial"}}, "platforms_rating": {"ratings_by_platform": "Average rating by platform"}, "play_wheel_of_fortune": {"desktop": {"go_to_smartphone": "Go to your smartphone", "scan_qr_code": "Our game is not accessible on the computer, flash the QR from your smartphone to directly access the game"}, "discover_gift": "discover your gift!", "display_draw_details": {"check_spam": "Remember to check your spam", "email_not_received": "Didn't receive an email?", "errors": {"could_not_save_data": "An error occurred while saving contact details", "email_could_not_be_sent": "An error occurred while sending the email", "phone_associated_with_other_email": "This phone number is already associated with another email address, please change it."}, "mail_resent": {"spam": "Remember to check your spam", "title": "<PERSON>ail sent successfully"}, "resend_mail": "Resend email", "see_mailbox": "See you in your mailbox! \n📬", "you_won": "Congratulations you won!"}, "errors": {"error_creating_draw": "An error occurred during the draw", "error_getting_wof": "Error retrieving wheel of fortune", "errors_getting_link": "An error occurred while retrieving the redirection link"}, "give_contact_details": {"birthday": "Date of birth", "email": "Email address", "errors": {"already_played": "This email address has already been used to play", "invalid_email": "Please enter a valid email", "invalid_phone": "Please enter a valid phone number"}, "firstname": "First name", "get_my_gift": "Receive my gift", "give_details_to_get_gift": "We need your contact details to send you your gift!", "i_agree": "I accept the", "lastname": "Name", "phone": "Phone number", "rules": "Terms and conditions", "you_won": "Well done, you won"}, "leave_review": {"after_leaving_review": "After leaving your review.", "after_subscribing": "After subscribing.", "come_back": "Come back to this page", "come_back_game_page": "Return to the game page", "discover_gift": "discover your gift!", "leave_review": "Leave a review", "leave_us": "Leave us a", "on": "on", "redirected_to": "You will be redirected to", "review": "review", "start_wheel": "Spin the wheel and", "subscribe": "Subscribe", "to_instagram_page": "to our Instagram page", "to_participate": "How to participate", "to_subscribe": "Subscribe"}, "play": "Play", "powered_by": "Powered with love by", "rules": {"sections": {"content": {"n1": "The business {{name}}, {{address}} (hereinafter referred to as the “<b>Organizer</b>”) is organizing a competition (hereinafter referred to as the “<b>Game</b >”), the terms of which are detailed herein. <br/><br/>The purpose of these regulations (hereinafter referred to as the “<b>Regulations</b>”) is to define the rules and conditions for participation in the Game set up by the Organizer. <br/><br/>The Game is run using the solution (hereinafter referred to as the “<b>Solution</b>”) made available to the Organizer by the company Malou Food Marketing, registered with the RCS of Créteil under number ***********, having its head office at 35 avenue du Maréchal de Lattre de Tassigny, 94220 Charenton-le-Pont (hereinafter referred to as “<b>MALOU</b>”). The Regulations are available on the Solution. The simple fact of participating in the Game implies full and unreserved acceptance of the Organizer's <a href='{{url}}' target='_blank'>Rules and Confidentiality Policy</a>.<br/><br/><b>It is expressly understood that MALOU's role is limited to make the Solution available to the Organizer. The determination of the terms of the Games is left to the discretion of the Organizer.</b>", "n10": "The Organizer processes the personal data of Participants. For more information on this subject, the Participant is invited to consult the <a href='{{url}}' target='_blank'>confidentiality policy</a> accessible during all use of the Game.<br/><br/>The Organizer whose contact details are provided at the beginning of the Game is responsible for processing their data.<br/><br/>The processing of data aims at organizing the Game and the draw, announcing the winners, as well as, if applicable, sending the prize won by the winners. The processing is based on the execution of the contract, namely this Game regulation. This data is kept for the duration of the Game (until the expiry date of the prizes) and then archived for a period of 5 years.<br/><br/>This data (email and/or telephone number) will be processed to inform them of the Organizer's offers. This processing is based on the participant's consent. This data may be processed for this purpose until consent is withdrawn or, failing that, for a period of 3 years from the last contact from the person concerned.<br/><br/>The data is processed by the Organizer on the basis of its legal obligations, for the purpose of processing the participant's request and retaining proof of it in the event of a dispute (kept during the processing of the request and then for 5 years in intermediate archiving).<br/><br/>Personal data may be disclosed, only for the purposes set out above, by the Organizer to its staff and its providers responsible for managing the Game and IT providers responsible for managing data and promotional offers (if applicable).", "n11": "The Regulations are governed by French law. In the event of a dispute relating to the interpretation and execution of the Rules, the Organizer will seek an amicable solution to the dispute with the Participant(s). In the absence of an amicable settlement, any dispute that may arise in connection with its validity, interpretation or execution will be submitted to the competent courts.<br/><br/>In the event that one of the clauses of the Regulations would be declared null and void, this would in no way affect the validity of the Regulations themselves and all its other clauses would retain their force and scope. For any questions relating to the operation of the Game.", "n2": "Participation in the Game is reserved for any natural person exposed to the animation, in particular through a QR code (hereinafter referred to as the “<b>Participants</b>”). <br/><br/>Any Participant who wishes to participate in the Game may be invited to carry out one or more actions predetermined by the Organizer (leave a review, subscribe to social networks, etc.). <br/><br/>Participation in the Game may be free or subject to a purchase condition.<br/><br/>In the event of refusal of all or part of the Rules, it is up to the Participants to refrain from participating in the Game. <br/><br/>Only one application per Participant (same name, same first name, same postal address) during the entire period of the Game is authorized. It is strictly prohibited for the same Participant to participate in the Game with several email addresses as well as to participate in the Game from the account opened for the benefit of another person. <br/><br/>It is strictly forbidden, by any means whatsoever, to modify or attempt to modify the Game devices offered, in particular in order to modify the results or to influence by automated means or unfair the validity of the draw. <br/><br/>If necessary, the Organizer authorizes itself to carry out all the necessary checks to verify the absence of fraud.", "n3": "The exact dates and duration of the Games are specified by the Organizer on the Solution. They reserve the possibility, at the end of the scheduled dates, to extend the Games under the same conditions. In this case, the new dates of the Games will be specified by the Organizer to the Participants.", "n4": "The prize (hereinafter referred to as the “<b>Prize</b>”) for the Game consists of a gift detailed on the Solution and offered to the Participant by the Organizer. The nature, value, stock involved, as well as the recovery conditions are left to the discretion of the Organizer.", "n5": "The Winners are determined by drawing lots. The draws are carried out automatically following participation using a random algorithm. With each participation, the algorithm will maximize the number of gifts distributed taking into account the quantities available. The winners (hereinafter referred to as the “<b>Winners</b>”) will then receive a “gift coupon” by email which will allow them to access their Prize.<br/><br/>The selection criteria are the sole choice and responsibility of the Organizer. Participants cannot contest them.", "n6": "The Winners must show their coupon received by email to the Organizer's staff. The Participant must necessarily have their gift voucher validated by the Organizer's staff to receive their Prize. <br/><br/>In the event that the Winner's email proves to be clearly incorrect, it will under no circumstances be up to the Organizer or MALOU to carry out research of any kind to find the winner. The latter would lose the benefit of their winning and would not be able to claim any compensation.<br/><br/>If a Winner refuses the Prize, the said Winner loses the benefit of their Prize.<br/><br/>The Prizes won cannot give rise to exchange, reimbursement or payment of their equivalent value in cash. <br/><br/>In the event that the Prize won by the Winner is no longer available for reasons beyond the control of the Organizer, the latter reserves the right to substitute another Prize for the Prize won, of the same type and of equivalent or greater unit commercial value.", "n7": "<ul><li>The Organizer has the right to eliminate from the Game or not take into account the participation of a Participant who does not comply with the provisions of the Regulations. </li><li>The Organizer cannot be held responsible if, in the event of force majeure as defined in article 1218 of the Civil Code or justified necessity, they were required to cancel the Game, shorten it, suspend it, extend it or modify its conditions. </li><li>The Organizer may cancel or suspend all or part of the Game if it appears that fraud has occurred in any form whatsoever. In this case, it reserves the right not to award Prizes to fraudsters and/or to prosecute the perpetrators of these frauds before the competent courts. </li></ul>", "n8": "The provision of Prizes is the sole responsibility of the Organizer. MALOU cannot provide any guarantee on these and cannot be held responsible for any defects in the Prizes or for any incidents that may occur during their delivery.<br/><br/>Participation in the Game implies knowledge and acceptance by the Participants of the characteristics, limits and risks of the technologies used by the Internet and the technologies linked to it, in particular with regard to technical performance, response times for consulting, querying or transferring information, the risks of interruption and more generally, the risks inherent in any connection or transmission, the lack of protection of certain data against possible misappropriation and the risks of contamination by possible viruses circulating on the network, without any liability on the part of The Organizer or MALOU for this. The Organizer or MALOU cannot be held responsible in the event of technical or human failures of telecommunications operators, nor in the event of a malfunction of the Internet network, nor in the event of a temporary or lasting technical failure.<br/><br/> The Winners are prohibited from any partial or total resale of the Prizes won and are solely responsible for the choice and use they make of the Prizes won.", "n9": "Participation in the Game implies absolute compliance with the Rules.<br/><br/>More generally, participation in the Game implies a loyal, responsible and dignified attitude involving in particular compliance with the rules of the Rules.<br/><br/>Participants undertake not to implement or seek to implement any participation process which does not strictly comply with respect for the principles of the Game and the Rules.<br/><br/>Thus:<br/><ul><li>The Participant does not have the right to use any program, mechanism or software which could interfere with the functions and/or the progress of the Game. </li><li> The Participant does not have the right to perform any action that would cause excessive slowdown of the technical capabilities of the Solution.</li><li>The Participant does not have the right to block, modify or reformulate the content of the Solution. Game. </li></ul><br/>The Organizer reserves the right to carry out any verification for the application of this article."}, "title": {"n1": "Article I: Organization and context", "n10": "Article X: Personal data", "n11": "Article XII: Applicable law and jurisdiction", "n2": "Article II: Conditions and terms of participation", "n3": "Article III: Duration", "n4": "Article IV: Endowment", "n5": "Article V: Designation of winners", "n6": "Article VI: Delivery of grants", "n7": "Article VII: Exclusion from the Game and liability", "n8": "Article VIII: Limitation of Liability", "n9": "Article IX: Compliance with the Regulations – Handling prohibited"}}, "title": "Terms and conditions"}, "select_restaurant": {"before_playing": "Before playing,", "know_where_to_pick_up": "To find out where to collect your gift", "select_restaurant": "select your restaurant"}, "start_wheel": "Spin the wheel", "turn_wheel_and": "Spin the wheel and", "wait": "Wait..."}, "post": {"comments": "comments", "commitment": "engagement", "copy_caption": "Copy caption", "like": "likes", "saved": "Save", "show_less": "See less", "show_more": "See more", "unsaved": "Unsave"}, "post_filter_options": {"comment": "Comments", "created_at": "Latest", "engagement": "Engagement", "impression": "Impressions", "like": "<PERSON>s"}, "posts": {"adapt_content": "Adapt content to SEO (keyword score...)", "add_post": "Create post", "add_redirect_link": "Add a redirection link", "alarm_off": "No date scheduled", "all": "All", "at": "at ", "cancel": "Cancel", "cancel_content": "Post could not be published", "check": "Published on ", "combined_actions": "Bulk actions", "connect_gmb": "You must connect Google ", "continue_without_cta": "Continue to post them without action buttons", "create_draft": "Create a draft", "current_month": "Current month", "current_year": "Current year", "custom": "Custom", "date": "Date", "delete": "Delete", "delete_post": "Delete post?", "delete_posts": "Delete posts?", "draft": "Draft", "duplicate": "Duplicate", "duplicate_in_seo": "To Local SEO", "duplicate_in_social": "In Social Media", "duplicate_media_info": "Only photos are accepted, the post will be duplicated without the video.", "duplicate_other_restaurants": "Duplicate to other businesses", "duplicate_post_modal": {"duplicate_without_buttons": "Duplicate without buttons", "failed_to_match_url": "Your {{word}} is not included in your information. \nWe were unable to adapt the action button to this business.", "keep_same_caption": "Keep the same caption for all posts", "loader_steps": {"step_1": "Scanning your post", "step_2": "Optimizing your keywords", "step_3": "Optimizing your caption", "step_seo_2": "Media optimization"}, "no_continue": "No, continue", "no_platform_connected": "No platform connected, duplication won't take this restaurant into account", "original_post": "Original post", "original_post_with_count": "Post original ({{index}}/{{total}})", "post_all_same_time": "Post all posts at the same time", "post_duplicated": {"=1": "Your post was duplicated", "other": "Your posts have been duplicated"}, "url_type": {"order_url": "online order link", "phone": "phone", "reservation_url": "booking link", "website": "website"}, "will_be_saved": {"=1": "It will be saved in {{target}}. Do you want to edit it now?", "other": "They will be saved in the {{target}} tab. Do you want to edit them now?"}, "yes_modify": "Yes, edit"}, "duplicate_social_post_modal": {"loader_steps": {"step_2": "Optimization of your hashtags"}}, "duplicated_post": "Duplicated post in Local SEO. You should edit it beforehand:", "duplication_failed": "The duplication failed", "duplication_modal": {"status": {"draft": "Draft", "later": "Schedule", "now": "Now", "pending": "Pending"}}, "duplication_succeeded": "Successful duplication", "duplication_success": "Successful duplication.", "edit": "Edit", "error": "Error", "error_occurred": "Oops, an error has occurred", "event_in_past": "Your event has passed, change the dates to duplicate it in other businesses.", "footer": {"posting_ended_with_errors": "Issue while posting", "posting_ended_without_errors": "Publication completed", "posting_in_progress": "In the process of being published"}, "gmb_disconnected": "Houston, we lost signal...", "gmb_disconnected_subtitle": "Please log in again from the platforms tab. \nYou can still create drafts.", "gmb_not_connected": "You may not be logged in to Google anymore, try logging in again from the home page and then add your business", "gmb_post_schedule_end_date_in_past": "The end date of the post event cannot be in the past", "goals": "Objectives", "image_alt": "Post Image", "improve_position": "We advise you to create posts to interact with your local customers and improve your rankings on your strategic keywords", "in": "The ", "info": "Information", "last_month": "Last month", "last_year": "Last year", "management_mode": "It looks like you are in management mode, you can't retrieve the posts", "modify": "Edit", "navigate_to_platforms_button": "Connect the platform", "new_post": {"accepted_formats": "Accepted formats: jpg and png", "add_action_button": "Would you like to add an action button?", "add_event_title": "Add title", "add_image": "Add a media to your post", "add_informations_to_use": "Fill in your information to use this button.", "add_offer_infos": "Add other information", "add_pics": "Add media", "add_url_in_information": "Fill in your website link in your information to pre-fill “learn more”.", "add_website_url": "Add my website", "ai_completion_error": "The generation with artificial intelligence has failed. \nTry Again", "ai_prompt_example": "Example: Generating a post about our new pistachio dish", "ai_remaining_credits": "({{remainingCredits}} remaining credits)", "ai_remaining_credits_mobile": "({{remainingCredits}} credits)", "ask_ai": "Ask AI", "at": "At: ", "cancel": "Cancel", "changes_have_been_made": "Changes have been made, do you want to save them?", "choose_file": "Upload file", "choose_keywords": "Choose your keywords to use this feature", "choose_option": "Choose a Call-to-action button", "close": "Close", "close_without_saving": "Close without saving", "code_placeholder": "A discount code or coupon", "delete": "Delete", "display": "or see", "draft": "Draft", "drag_here": "Drag here or", "duplicate_in": "Duplicate in the Social Media section", "edit_pic": "Edit media", "edit_with_ai": "Edit with AI", "end_date": "End date", "error": "Error", "event": "Event", "event_dates": "Event dates", "event_end_before_start": "The start date of your event must occur before end date.", "event_needs_end_date": "The event must have an end date", "event_needs_end_date_future": "The event must have an end date in the future", "event_needs_start_date": "The event must have a start date", "event_should_have_title": "The event must have a title", "fetching_image_failed": "The media could not be downloaded. Please try to modify it manually or choose another one.", "file_must_be": "The media file must be PNG or JPEG", "format_not_supported": "This format is not supported", "improve_ref": "The media is renamed with your keywords to improve your SEO ranking", "keyword_gauge_title": "Post score", "large_file": "The media file is heavy, it might not be accepted", "later": "Schedule", "link_placeholder": "A link to take advantage of the offer", "media_not_created": "The media could not be created", "message_too_long": "Must be at most 1500 characters", "my_gallery": "See media library", "my_keywords_list": "My keywords list", "new": "New", "no_action": "None", "now": "Now", "offer": "Offer", "operation_not_carried": "The operation could not be carried out, the business may need to be validated from the Google interface", "optimize": "To optimise", "optional": "(optional)", "or": "or", "pic_name": "Media name", "picture_name": "Picture name", "planned_post": "Planned post", "post_caption_contains_phone_number": "Google does not allow a phone number in your post", "post_caption_text_limit": "Your text must be less than {{captionTextLimit}} characters", "post_description_placeholder": "Write your post here.", "post_in_progress": "Publishing in progress", "post_legend": "Caption", "post_on": "Post on", "post_type": "Post type", "publication_error": "Publishing error", "publish": "Publish", "publish_date": "Publishing date", "reconnect_platform": "Please reconnect the platform from the Platforms tab, then try again", "save_as_draft": "Save as a draft", "save_url": "Save link", "saved_post": "Saved post", "seems_error": "It seems that there is at least one error on the media: <br>", "should_we_save": "Attention", "small_pic": "The media file is too small, try to choose a bigger one", "start_date": "Start date", "terms_placeholder": "Terms of use", "textarea_loading_text": {"ai_can_translate": "AI can translate your text into", "ai_limit_reached": "AI usage limit reached", "ai_response_loading": "AI is thinking, taking into account", "lang_of_your_choice": "the language of your choice", "optimized_text_length": "optimized text size", "post_subject": "the subject of the post", "your_keywords": "your keywords"}, "too_small_dimensions_image": "Minimum {{ minimumWidth }}x{{ minimumHeight }}px (currently: width {{ actualWidth }}, height {{ actualHeight }})", "too_small_size_image": "Image too small, minimum {{ minimumSize }}kB (currently: {{ actualSize }}kB)", "translate_selection": "Translate selection", "unknown_error": "Unknown error", "unset": "- Not specified", "update": "Update", "updated_post": "Updated post", "url": "Call-to-action link", "url_action_invalid": "The action button url is invalid", "url_action_should_not_be_empty": "The url cannot be empty for this action", "url_offer_invalid": "The offer url is not valid", "url_placeholder": "http://mywebsite.fr", "write_message": "Write a caption for your post.", "write_post": "Write your post here."}, "no_media": "This post has been rejected because it does not contain media. \nEdit the post to add media and try again.", "no_post": "When life gives you a blank page, make a decision...", "no_post_subtitle": "Create a post or change your filter", "not_for_video": "You cannot upload a video on Google", "not_instagram_business": "The Instagram account is not a Business account. \nPlease change your account settings on Instagram to be able to post.", "not_published": "Not published yet", "offer_in_past": "Your offer has passed, change the dates to duplicate it in other businesses.", "open_platform": "Open on platform", "operation_failed": "The operation could not be carried out, the business may need to be validated from the Google interface", "operation_failed_retry": "It seems like the platform rejected the post, please retry.", "pagination": {"items_per_page_label": "Posts per page"}, "planned": "Scheduled", "post_for": "Why should you create posts?", "post_stuck_processing": "Post has been rejected by Google", "posts_duplicated": "Your posts have been duplicated in the businesses you have selected", "posts_list": {"post_not_found": "This post does not exist."}, "publication": "Published", "publication_in_progress": "In progress", "publish_as_is": "Publish anyways", "published": "Published", "reconnect_platform": "Your Facebook connection has expired. \nReconnect Facebook and try again.", "rejected_by_platform": "It appears that the platform has rejected this post. Please duplicate the post and then edit it before trying again.", "search": "Search", "select": "Select (0)", "seo": {"list": {"select_items_before_duplicate": "Select posts to duplicate them", "select_more_to_delete": "Select posts to delete them", "title": "Google posts"}, "new_post": {"title": "Create a Google Post"}}, "service_temporarily_unavailable": "The publication was temporarily unavailable. \nPlease try again.", "some_posts_cant_be_duplicated_user_role": "Duplication did not work on certain restaurants because you do not have permissions", "some_posts_missing_cta": "Some of your posts don't have an action button", "status": "Status", "sync": "Synchronize", "synchronize_button": "Synchronize", "teams_resolve": "Our team are doing their best to fix the issue", "unknown_error": "Unknown error", "update_publish_date": "Edit the publishing date", "want_delete_post": "Do you want to delete this post (it will also be deleted from the platforms on which it was published)?", "want_delete_posts": "Do you want to delete these posts (they will also be deleted from the platforms on which they were posted)?", "watch_later": "Coming up on", "yes_delete": "Yes, delete"}, "profile": {"cgu": "TOS", "email": "E-mail", "feedbacks_notifications_dm": "Receive an email when I receive a private message", "feedbacks_notifications_dm_details": "An email as soon as you receive a private message", "file_too_big": "File too large", "firstname": "First name", "lastname": "Last name", "report": {"need_selected_restaurants": "You must select businesses to generate your review report"}, "role": "Role", "saved": "Saved changes", "unknown_error": "unknown error"}, "published_filters": {"all": "All", "draft": "Drafts", "not_published": "Not published yet", "pending": "Scheduled", "published": "Published"}, "rating_filters": {"=0": "Without ratings", "other": "#"}, "reel_media": {"upload_text_1": "Select cover photo or download from ", "upload_text_2": "my computer", "upload_text_3": " or ", "upload_text_4": "my gallery"}, "report": {"no_review": "No review for the selected period of time"}, "reports": {"reviews": {"pdf": {"average_rating": "Average rating", "overall_average": "Average rating of your reviews on all your platforms", "page_title": "Your report has arrived!", "reviews": "reviews", "unanswered_reviews": "Unanswered reviews", "zoom_on_your_reviews": "Focus on your reviews"}}, "unsubscribe": {"confirm_email_daily_reviews": "Enter your email address to confirm deactivation of your daily reviews report", "confirm_email_monthly_performance": "Enter your email address to confirm deactivation of your monthly performance report", "confirm_email_weekly_performance": "Enter your email address deactivation of your weekly performance report", "confirm_email_weekly_reviews": "Enter your email address to confirm deactivation of your weekly reviews report", "daily_reviews_subtitle": "You're about to opt out <strong>{{ email }}</strong> of your daily reviews report", "daily_reviews_title": "You will no longer receive this daily report of your reviews", "monthly_performance_subtitle": "You're about to opt out <strong>{{ email }}</strong> of your monthly performance report", "monthly_performance_title": "You will no longer receive this monthly performance report", "unsubscribe_success_subtitle": "You will not see this report again", "unsubscribe_success_title": "Deactivation successful!", "weekly_performance_subtitle": "You're about to opt out <strong>{{ email }}</strong> of your weekly performance report", "weekly_performance_title": "You will no longer receive this weekly performance report", "weekly_reviews_subtitle": "You're about to opt out <strong>{{ email }}</strong> of your weekly reviews report", "weekly_reviews_title": "You will no longer receive this weekly report of your reviews", "wrong_email": "The specified email address is not yours"}}, "restaurant_ai_settings": {"ai_assistant": "AI Assistant", "details": {"catchphrase": "Catchphrase", "customer_naming": "Nomination", "default_language_response": "Reply to reviews without comment", "reply_tone": "Formal/Informal (reviews in french)", "restaurant_name": "Restaurant name", "signature": "Signatures"}, "header": {"subtitle": "This information allows us to offer you the best responses to reviews", "title": "Setting up your review responses with artificial intelligence"}, "modals": {"duplicate": {"error": "An error occurred while duplicating the form", "personalize_duplication": {"restaurant_name": "Restaurant name", "signature": "Signature", "signature_placeholder": "Ex: The {{restaurantName}} team", "subtitle": "Edit the elements"}, "restaurant_selection": {"subtitle": "Select the businesses to which you wish to duplicate your form, you will be able to modify the name and signature of each location."}, "success": "The form has been duplicated to the selected businesses", "title": "Duplicate to other businesses"}, "upsert": {"default_legend": "new dessert: homemade banana and walnut cake every day", "error_generating_preview": "An error occurred while generating a response to the review.", "errors": {"max_forbidden_words": "You have reached the maximum number of forbidden words ({{max}})", "missing_customer_naming": "Nomination is mandatory", "missing_default_language": "Response default language is mandatory", "missing_reply_tone": "Tutoiement/Vouvoiement (reviews in french) is mandatory", "missing_restaurant_name": "Restaurant name is mandatory", "upsert_error": "An error occurred while saving the settings"}, "example_preview": "Delicious brunch, I loved my truffled brioche and my chocolate pizza. \nThe place is friendly and the decoration nice. \nI recommend!", "generate": "Generate", "keyword_score": "Keyword Score", "preview": "Preview", "proposal": "Suggestion", "retry": "Try again", "reviewer_name": "<PERSON>", "success": "The changes have been saved", "tabs": {"advanced": {"catchphrase": {"placeholder": "Ex: <PERSON><PERSON><PERSON>, Hello, Dear", "title": "Catchphrase"}, "forbidden_words": {"placeholder": "E.g. hygiene", "title": "Forbidden words"}, "keywords": {"empty": "No keywords were generated. Go to the resources/keywords page to select your list of keywords. ", "empty_cta": "Generate my keywords now", "title": "Keywords to use in the response"}, "prompt": {"placeholder": "Personalize your AI Assistant (ex: use an engaging tone and add some humor when appropriate)", "title": "Advanced Prompt"}, "title": "Advanced settings"}, "general": {"catchphrase": {"title": "Catchphrase"}, "customer_naming": {"title": "Nomination"}, "default_language_response": {"title": "Reply to reviews without comment"}, "do_not_translate": "Do not translate", "reply_tone": {"title": "Formal/Informal (reviews in French)", "title_post": "Formal/Informal"}, "restaurant_name": {"title": "Restaurant name"}, "signature": {"add_one_or_several": "Add one or more signatures (only one is randomly selected for each reply)", "need_one_signature": "You must have at least one signature", "placeholder": "Ex: The {{restaurantName}} team", "title": "Signature"}, "title": "General settings"}}, "title": "Settings for your AI assistant review replies", "with_comments": "5 stars with comment", "without_comments": "5 stars without comments"}}, "robot_ready": "Your AI assistant is ready!", "robot_ready_content_1": "Using the captions you write, we've personalized your AI assistant with your writing style.", "robot_ready_content_2": "You can update it whenever you want."}, "restaurant_edit_locations": {"city": "City", "nearby_places": "Nearby places", "postal_code": "Postal code"}, "restaurant_list": {"internal_name_updated": "The name of the restaurant has indeed been changed!"}, "restaurants_list": {"brand_account": "Brand account", "business": {"add_first_restaurant": "Add your first business to take full advantage of the MalouApp", "add_restaurant": "Add a business", "address": "Location", "brand": "Brand", "business": "Business", "card_view": "Card view", "delete": "Stop managing", "last_modification": "🕙 Last modification, ", "last_update": "Last update", "list_view": "List view", "local_business": "Local business", "no": "Cancel", "no_restaurants": "No business", "no_results": "No location matches your search.", "notifications": "Notifications", "search": "Search", "type": "Type", "unknown_error": "Unknown error", "yes": "I understand"}, "connect_restaurant": {"add_a_restaurant": "Add a business", "add_organization": "Choose an organization", "add_your_restaurant": "Add your business", "brand": "Brand", "brand_description": "Business without address", "brand_text_html": "To <strong>create your brand account</strong> on the MalouApp, <br/> link your <strong>Facebook admin account</strong> to your brand page.", "choose_type": "Choose a business type", "connect": "Connect my business", "connect_facebook": "Connect your Facebook page", "connect_gmb": "Connect with Google", "error": "An error occurred", "local_business": "Venue", "local_business_description": "Business with address", "local_business_text_html": "To <strong>create your facility</strong> on the MalouApp, <br/> link your business's Gmail admin account", "validate_credentials": "Validate access"}, "edit_internal_name": {"duplicate_name": "This name already exists, please choose another one.", "edit_name": "Change the name of the business", "edit_name_description": "This name is dedicated to your internal management, it will only appear on the MalouApp. \nThe name of the location as it appears on the platforms is located in the Information page."}, "last_update": "Last update", "navbar": {"stats": "AGGREGATE INSIGHTS", "your_business": "Your Businesses"}, "no_restaurants": {"funny_message": "Well then, we don't have a business?"}, "no_results": {"funny_message": "I don't see anything"}, "welcome_modal": {"add_first_business": "Add my business", "at_malou": "to Malou!", "follow_steps": "Follow the steps to make your business online on all platforms to attract customers", "ready_to_start": "Ready to get your business off the ground?", "welcome": "Welcome"}}, "reviews": {"activated": "activated", "advanced_options": "Advanced options", "all_reviews_answered": "Congratulations, you have answered all your reviews!", "analysis_not_available": "Semantic analysis is not available in this subscription", "anonymous": "Anonymous", "answered": "Answered", "archive": "Archive", "auto_replies_disabled": "Automatic replies disabled", "auto_replies_enabled": "Automatic replies enabled", "auto_reply": "Auto reply", "auto_reply_on": "Auto-reply is on", "auto_reply_recent_reviews": "Unanswered synchronized reviews from the day before will be answered automatically", "cant_edit_review_while_sync": "Wait for sync to complete to edit your reply", "cant_reply_because_date": "You have exceeded the deadline to respond to the review", "cant_reply_because_date_short": "Deadline exceeded", "cant_reply_review_while_sync": "Wait for sync to complete to respond to this review", "comments": "Comments", "connect_platform": "Connect this platform from the tab Platforms", "connect_platforms": "Connect platforms", "courier_to_restaurant": "Delivery ", "customer_to_restaurant": "Customer", "day_left_to_reply": "(1 day left)", "days_left_to_reply": "({{ days }} days left)", "deleted": "Review removed", "desactivated": "desactivated", "download_info": {"size_limit": "PDF export of reviews is limited to 700 reviews, please try again by modifying the filters."}, "download_insights": {"csv": "CSV Format", "pdf": "PDF Format"}, "edit": "Edit", "enable_automated_response": "To configure automatic reply templates click here", "end_of_reviews": "All your reviews have been loaded. \nChange your filters to view other reviews.", "error": "Error", "error_explain": "We were unable to send your reply, please check your connection to the platform", "error_occurred": "Oops, an error occurred, but the text has been copied", "errors": {"change_error": "Please replace the text (TO CHANGE) with keywords", "chevron_error": "Please replace the texts between <<>> with keywords", "emoji_error": "Zenchef doesn't support emojis. Please remove them"}, "facebook_error": "We could not get data from Facebook/Instagram", "filters": {"all": "All", "answered": "Replied", "archive": "Archives", "archived": "Archived", "comments": "Comments", "date": "Date", "notAnswerable": "No reply possible", "notAnswered": "Not replied", "pending": "Pending", "period": "Period", "platform": "Platform", "platforms": "Platforms", "rating": "Rating", "ratings": "Ratings", "status": "Status", "unarchived": "Unarchived", "withText": "With comment", "withoutText": "Without comment"}, "first_order": "First order", "general_html": "<b class=\"mr-2\">General : </b>", "get_more_reviews": "Get more reviews", "get_reviews": "Retrieve reviews", "get_reviews_from_connected_platforms": "Retrieve reviews from connected platforms", "go_to_automations": "Go to automations", "go_to_resources_for_templates": "Go to \"Resources > Review templates\" for each of your restaurants to set up their automation", "grade": "Reviews", "never": "never", "no_connected_platforms": "Itineris, I don't understand you anymore, Do you understand me now? Oh, I'm getting it all the time right now...", "no_reviews": {"with_filters": "Mayday mayday! \nWe can't see anything!", "without_filters": "If I may say so, I kinda agree with everyone else."}, "no_reviews_due_to_search": "No reviews match your search...", "not_answered": "Not answered", "offer_sent": "Offer sent", "options": "Options", "orders": {"=0": "", "=1": "New client - 1 order", "other": "# orders"}, "other_filters": {"show_archived": "Show archived", "show_private": "Show private"}, "other_replies": {"=1": "1 other reply", "other": "# other replies"}, "pending": "Pending", "pending_explain": "Your reply has been sent, it is awaiting validation by the platform", "platforms": "Platforms", "please_connect_platforms": "Please log back into the platforms tab to access your Customer Reviews", "ranking_filter": {"no_grade": "No review", "star": "star", "stars": "stars"}, "recovery_in_progress": "Retrieval in progress...", "redirect_to_platform": "You will be redirected to the platform to leave your reply", "rejected": "Rejected", "reply": "Reply", "reply_modal": {"add_and_next": "Submit and move on", "ai_error": "The generation with artificial intelligence has failed. Try Again", "ai_remaining_credits": "({{ remainingCredits }} remaining credits)", "ai_suggestion": "Proposed reply made by AI", "ai_tooltip": "Generate a reply tailored to the review containing your keywords", "answer": "Answer", "ask_ai": "Ask AI", "automation": "Automation", "cant_reply": "You cannot respond to this review", "cant_save_template": "Reply template could not be created", "check_permissions_or_retry_reply": "Please check your connection settings or try again later.", "choose_model": "Choose a template", "choose_signature": "Choose a signature", "confirm_review_response": {"deliveroo": "Your reply will be posted on the platform", "email_will_be_sent": "The reply to the private review will be send by email to the customer. ", "facebook": "Your reply will be posted on the platform", "gmb": "Your reply will be posted on the platform", "lafourchette": "The text has been copied. \nA tab will open on the management platform of TheFork", "pagesjaunes": "The text has been copied. \nA tab will open on the management platform of PagesJaunes (Solocal)", "tripadvisor": "Your reply will be posted on the platform", "ubereats": "Your reply will be posted on the platform", "yelp": "A tab will open, all you have to do is paste the text", "zenchef": "Your reply will be posted on the platform"}, "edit_reply": "Edit reply", "edit_with_ai": "Edit with AI", "error": "Error", "error_review_tripadvisor": "The review could not be posted on TripAdvisor, but the text has been copied and a tab just opened, you only have to paste the text directly onto the platform.", "filter_comments": "Filter comments", "filter_grade": "Filter reviews", "filter_models": "Filter templates", "generate_with_ai": "Generate with AI", "intelligent_subject_headband": {"risky_subject": "risky subject", "risky_subject_text": "We didn't respond to this review because it's considered as comprising a"}, "keyword_score_gauge": "Reply score", "last_used": "Last used", "message": "Message", "new_model": "Create a new template", "new_reply": "Write a new reply", "new_signature": "Create a new signature", "next": "Next", "no_emails": "There is no email address from which we can send the reply", "optimize": "Optimize", "placeholder": "Example:\n\n\nHello @Customer Name,\n\n\nThanks for your review....\n\n\n@Business name team", "placeholder_ai": "Write your reply or", "platform_disconnected": "{{platform}} is disconnected", "private": {"from": "From ...", "no_available_emails": "No valid email has been attached to the organization. \nYou can do this from the Campaigns tab.", "object": "Object"}, "publish": "Submit", "rating_selection_limit": "Each model can correspond to 2 neighboring notes maximum", "ratings": ["Neutral customer (3 stars)", "Satisfied customer (4-5 stars)", "Unsatisfied customer (1-2 stars)"], "reconnect_platform": "Reconnect {{platform}}", "reconnect_platform_to_reply": "Reconnect {{platform}} to answer", "reply": "Submit", "reply_and_next": "Submit and go to next review", "reply_copied": "Reply copied. Paste it into the text space on the platform", "reply_pending": "Reply pending, it will be validated by the platform within a few hours", "reply_sent": "Reply submitted", "respond_to_review": "Reply to the review", "retry_reply": "Please try again", "review": "Review", "review_doesnt_exist": "It seems that this review no longer exists on Google. \nDo you want to delete it on the MalouApp?", "review_without_rating": "Review without rating", "send_answer": "Save the reply", "single_template_to_validate": "1 other template to validate", "star": "star", "stars": "stars", "templates": {"adjacent_ratings": "Each template can correspond to a maximum of 2 adjacent ratings", "auto_replies": "Automatic replies", "language": "Language", "no_templates": "No model match your filter selection.", "rating": "Ratings", "ratings": "Ratings", "review_type": "Type of reviews affected by this template", "save_as_template": "Save as template", "show_review_templates": "See my reply templates ({{templateCount}})", "template_name": "Type a new template name or select an existing one to edit", "title": "Reply template", "update_warning": "Warning, this reply template will be overwritten"}, "templates_to_validate": "{{number}} other templates to confirm", "text_error": "Your text may contain errors.", "textarea_loading_text": {"ai_can_translate": "AI can translate your text into", "ai_limit_reached": "AI usage limit reached", "ai_response_loading": "AI is thinking, taking into account", "lang_of_your_choice": "the language of your choice", "received_review": "the notice received", "usual_tone": "your usual tone", "your_keywords": "your keywords"}, "translate_selection": "Translate selection", "update": "Update", "update_and_next": "Edit and move on", "with_comment": "Review with comment", "with_or_without_comment": "Review with or without comment", "without_comment": "Review without comment", "yes": "yes", "yes_delete": "Yes, delete", "your_answer": "Your reply"}, "review_analyses": {"atmosphere": "Ambiance", "dish": "Flat", "environment": "Environment", "expeditiousness": "Wait Time", "food": "Food", "hygiene": "Hygiene", "no_results": "This review does not have a category associated with a sentiment", "not_analyzed": "This review could not be analyzed", "not_yet_analyzed": "This review has not been analyzed yet, you will be able to see the result within 24 hours", "other": "Other", "overall_experience": "Overall Experience", "platform_not_supported": "The analyses of the delivery platforms are not available", "price": "Price", "review_too_old": "This review is too old to be analyzed", "semantic_analysis": "Semantic Analysis", "service": "Service", "show_original_text": "View the original text to see the semantic analysis", "waiting": "Wait Time"}, "reviews": "Reviews", "reviews_delay": "We are retrieving {{ platformName }} reviews, your reviews should appear within 5 minutes.", "search": "Search", "show_original": "View the original (in {{lang}})", "sync_delayed": "{{platformName}} reviews may take a few minutes to be added to the MalouApp", "sync_error": "Synchronization error", "synchronization": "Synchronization", "synchronization_footer": {"retrieving_reviews": "Retrieving reviews in progress", "retrieving_reviews_async": "Retrieving reviews in progress, this may take several minutes", "synchronization_failed": "Problem during synchronization", "synchronization_finished": "Synchronization completed", "synchronization_in_progress": "Synchronization in progress", "synchronization_in_progress_of_your_restaurants": "Synchronization in progress for your restaurants"}, "text": "Comments", "text_copied": "Your text has been copied", "text_filter": {"with_text": "Reviews with text", "without_text": "Reviews without text"}, "toast": {"archived": "Archived", "review_not_found": "This review has been deleted by its owner", "unarchived": "Unarchived"}, "toggle_auto_reply": "Enable auto-reply", "totem": "Totem", "translate_into_app_lang": "Translate in English", "translated": "Translated", "translating": "Translating...", "try_to_sync": "Try to sync", "unarchive": "Unarchive", "updated_at": "Updated at", "updated_on": "Updated on:", "use": "Use"}, "reviews_count_table": {"restaurant_name": "Business", "review_count": "Review count"}, "reviews_ratings_average_table": {"average_rating": "Average rating", "restaurant_name": "Business"}, "reviews_synchronization": {"error": {"social_link_malformed": "The link to the platform is corrupted. \nTry disconnecting then reconnecting the platform. \nIf that doesn't work, contact support."}}, "roi": {"aggregated_estimated_customers": {"additional_clients": "Additional customers", "additional_clients_similar_locations": "Additional customers for similar businesses", "earnings": "Earnings in {{currency}}", "title": "Estimate by location"}, "aggregated_performance": {"answered_reviews_count": "Review answer rate", "average_answer_time": "Answer time", "errors": {"multiple": {"no_data": "We could not analyze your performance because some data is missing for the following businesses: {{ restaurants }}."}, "one": {"no_data": "Data are missing for the estimate."}}, "google_post_count": "Google posts", "google_score": "Google rating", "keyword_in_top_ten": "Keywords in the top 10", "keywords_score": "Keyword score", "no_data": "We could not analyze your performance because some data is missing.", "performance_score": "Perf. score", "positive_review_count": "Positive reviews", "restaurant_name": "Business", "review_count": "Review count", "social_impressions": "Social Media Impress.", "social_media_post_count": "Social posts", "title": "Performance by business"}, "aggregated_settings": {"average_revenue_per_month": "Average revenue per month*", "average_ticket": "Average ticket per customer*", "close_already_configured_restaurants": "Hide already configured businesses", "congrats_several_months_with_malou": "<PERSON>, several months of work with <PERSON><PERSON>!", "currency": "Currency*", "duplicate_settings": "Duplicate settings", "need_at_least_two_restaurants": "At least two businesses have to be set up", "open_already_configured_restaurants": "See already configured businesses", "remaining_restaurants": "{{restaurantWithoutRoiSettingsCount}}/{{restaurantCount}} businesses configured", "restaurant_category": "Type of business*", "settings_count": "({{current}}/{{total}})", "title": "Finish setting up your businesses"}, "client": "customer", "clients": "customers", "connect_gmb": "Connect Google to access your statistics.", "contact_us": "Please try again or contact customer service.", "contact_us_if_unusual": "Please contact customer service if this appears to be an error.", "details": "Details", "estimated_customers": {"booking": "Booking", "call": "Call", "clicks": "({{value}} clicks)", "come_back_next_month": "Come back on the 5th to discover your estimation", "customers_details_modal_subtitle": "thanks to marketing since you have been using <PERSON>ou", "customers_details_modal_title": "Details of your additional customers", "direction_request": "Itinerary", "email": "Contact by email", "gmb_explanations": "We take your settings and performance score into account to estimate the conversion of your Google actions into visits.", "impressions": "Page impressions", "instagram": "(Instagram)", "invalid_data": "Many statistics are missing to provide a reliable estimate.", "menu": "<PERSON><PERSON>", "message_sent": "Send message", "missing_data": "The estimate provided was made with missing data because we were unable to retrieve it for each date.", "order": "Order", "saves": "Records", "seo": "SEO", "shares": "Shares", "social": "Social networks", "social_explanation": "We take your settings and performance score into account to estimate the conversion of your Facebook and Instagram actions into visits.", "title": "Additional customers thanks to marketing", "tooltip_sales": "Developed with our database of businesses similar to yours (category, specialties, price, location)", "vs_other_similar_businesses": "VS similar businesses: {{min}} - {{max}} customers", "vs_other_similar_businesses_sales": "VS similar businesses:", "website": "Website", "will_be_estimated_next_month": "Your additional customers will be estimated starting next month", "will_be_estimated_this_month": "Your additional customers will be estimated this month"}, "estimated_customers_and_performance_chart": {"could_not_get_data": "We were unable to retrieve data for this month", "customer_y_axis_title": "Customers", "data_not_ready": "Available starting from the 4th of {{month}}", "data_unavailable": "Data not available", "estimated_customers": "Additional customers", "estimated_customers_thanks_to_marketing": "Additional customers thanks to marketing", "few_days_left": "Only a few days left to access your {{month}} estimates", "from_fourth_of_month": "Estimates are available from the 4th of the following month.", "performance_score": "Performance score", "restaurant_closed": "Restaurant closed", "restaurant_opened": "Restaurant open", "score_y_axis_title": "Score", "similar_restaurants": "Performance score of similar businesses"}, "filters": {"scale": "Scale"}, "missing_informations": "Missing information", "no_access_before_first_month": "This page will be available at the end of your first full month at Malou", "no_insights_error": "Oops, an error occurred while calculating your winnings.", "no_roi_for_brand_restaurants": "The earnings are not available for brand accounts", "not_enough_data": "There is not enough data to view other periods.", "performance_score": {"4_5_stars": "(4 and 5 stars)", "answer_time": "Response time to Google reviews", "answered_reviews": "Google Reviews answered", "average": "(average)", "facebook_posts": "Facebook Posts", "gmb_posts": "Google Posts", "gmb_rating": "Google rating", "instagram_posts": "Instagram Posts", "keyword_score": "Keyword Score", "keywords_in_top_ten": "Keywords in the top 10", "positive_reviews": "Positive sentiments", "reach_hundred": "How to reach 100/100", "received_reviews": "Google Reviews received", "social_impressions": "Social Media Impressions", "title": "performance score", "vs_other_similar_businesses": "VS similar businesses: {{score}}/100"}, "platforms": {"gmb_not_connected": "Check your Google my Business connection"}, "platforms_not_connected": "Connect your Google, Instagram, and Facebook platforms to access your statistics.", "platforms_not_connected_title": "Uh oh, we didn't find any data", "restaurant_settings_not_configured": "Please configure this businesses to be able to select it.", "restaurant_setup_modal": {"average_revenue_per_month": "Average revenue per month*", "average_revenue_per_month_placeholder": "Ex: 40,000 - 50,000", "average_ticket": "Average ticket per customer*", "average_ticket_placeholder": "Ex: 46", "confirm": "Just a few information to receive customized tips!", "currency": "<PERSON><PERSON><PERSON><PERSON>", "data_is_confidential": "This data will remain confidential.", "opening_date": "Opening date*", "restaurant_category": "Business type*", "subtitle": "This data will remain confidential.", "title": "Just a few information to receive customized tips!"}, "saved_time": {"and": "and", "campaign": "to gather reviews of {{ nbClients }} clients with a click", "campaign_title": {"multiple": "{{ nbCampaign }} Review Campaigns", "one": "{{ nbCampaign }} Review Campaign"}, "duplication": "{{ nbPosts }} duplicate posts and stories \n{{ nbElements }} information duplicated between your businesses", "duplication_title": "Duplication", "generated_by_ia": "generated for you by artificial intelligence", "hours_modal_title": "Details of your time saved", "including": "including", "information_update": "{{ nbUpdates }} updates made on Malou for all your platforms at once", "information_update_title": "Information Update", "interaction": "{{ nbComments }} comments, {{ nbMentions }} mentions and {{ nbMessagesSent }} messages from one single place", "interaction_title": "Interactions", "reports": "{{nbReports}} reports enabled on {{totalReports}}", "review_reply": "{{ nbReviews }} reviews centralized and answered automatically thanks to AI", "review_reply_title": "Reviews", "saved_hours_thanks_to_malou": "saved thanks to <PERSON><PERSON>", "sent_reports": "sent to different recipients", "seo_post": "{{ nbPosts }} Google posts", "seo_post_details": "including {{ nbLegends }} descriptions generated for you by AI", "seo_post_title": "Google posts", "social_post": "{{ nbPosts }} posts", "social_post_hashtags_detail": "{{aiGeneratedHashtags}} hashtag selections", "social_post_legend_detail": "{{nbLegends}} descriptions", "social_post_title": "Social Media Posts", "stories": "{{nbStories}} scheduled stories", "stories_title": "Instagram Stories", "time_saved": "Time saved"}, "settings": {"average_revenue_per_month": "Average revenue per month", "average_revenue_per_month_placeholder": "Ex: 40,000 - 50,000", "average_ticket": "Average ticket per customer*", "average_ticket_placeholder": "Ex: 46", "confirm_settings": "Confirm your settings to view your estimated earnings.", "congrats_several_months_with_malou": "Well done, more than {{months}} months of work with <PERSON><PERSON>!", "creation_in_progress": "Your earnings are being calculated...", "currency": "<PERSON><PERSON><PERSON><PERSON>", "data_is_confidential": "This data will remain confidential.", "duplicate_with_button": "Duplicate them quickly using this button.", "enter_parameters": "Enter your settings to view your estimated earnings.", "new_settings_effect": "This new configuration will apply for your results beginning next month. It will not overwrite your previous estimations.", "not_retroactive": "This new configuration will apply for your results beginning next month. It will not overwrite your previous estimations.", "restaurant_category": "Type of business", "same_data_for_multiple_locations": "Is this data the same for several of your businesses?", "see_earnings": "See my earnings", "settings": "Settings", "you_can_leave_while_creating": "No worries, the estimation will still be calculated if you leave this page."}, "tips": {"buttons": {"activate": "Activate", "answer": "Answer", "boost": "Boost", "check": "See", "check_tips": "See tips", "complete": "Complete", "download": "Download", "modify": "Edit", "post": "Post", "try": "Try"}, "congrats": "Congratulations !", "congrats_subtitle": "You've been using <PERSON><PERSON> like a chef.", "congrats_subtitle_with_encouragement": "The results of marketing efforts take time to bear fruit, but you're on the right track.", "congrats_with_encouragement": "Well done on your efforts, keep it up !", "error": "An error has occurred", "items": {"booster": {"booster_no_campaigns_description": "To collect reviews from your previous customers", "booster_no_campaigns_title": "Launch a review campaign", "booster_no_totems_description": "To collect up to 4x more reviews", "booster_no_totems_title": "Set up totems for your businesses", "booster_no_wheel_of_fortune_description": "To bring your customers back and get up to 5x more reviews", "booster_no_wheel_of_fortune_title": "Set up a wheel of fortune"}, "google": {"google_average_keywords_score_too_low_description": "Google looks out for businesses that follow their best practices", "google_average_keywords_score_too_low_title": "Look out for your keyword usage score", "google_no_action_button_in_posts_description": "To entice actions (Call, Book...)", "google_no_action_button_in_posts_title": "Add action buttons to your Google posts", "google_post_count_too_low_description": "To reach up to 20% more appearances", "google_post_count_too_low_title": "Post every week on Google", "google_same_seo_post_type_description": "Posts of the Offer/Event type allow to double the appearances.", "google_same_seo_post_type_title": "Vary your Google post types"}, "information": {"information_data_not_updated_for_3_months_description": "Google pushes pages with opening hours that are up to date", "information_data_not_updated_for_3_months_title": "Don't forget to regularly update your information", "information_empty_description_description": "Google pushes pages with a description with 500+ characters", "information_empty_description_title": "Don't forget to enter a description ", "information_missing_data_description": "Display coherent information on all your platforms", "information_missing_data_title": "Don't forget to fill in all your information"}, "interaction": {"interaction_unanswered_comment_and_mention_count_too_high_description": "Instagram looks up for regular accounts", "interaction_unanswered_comment_and_mention_count_too_high_title": "Answer all your comments and mentions", "interaction_unanswered_message_count_too_high_description": "Platforms take into account your response time", "interaction_unanswered_message_count_too_high_title": "Quickly answer to your messages"}, "keywords": {"keywords_not_updated_for_3_months_description": "They should vary according to seasons and calendar events", "keywords_not_updated_for_3_months_title": "Don't forget to regularly edit your keywords", "keywords_same_popularity_description": "Select keywords with different popularities", "keywords_same_popularity_title": "Vary your keyword selection"}, "mobile_app": {"mobile_app_not_being_used_description": "To receive alerts on your tasks to do", "mobile_app_not_being_used_title": "Download Malou mobile app"}, "reviews": {"reviews_atmosphere_regularly_mentioned_in_negative_analysis_title": "Pay attention to your atmosphere", "reviews_automation_not_activated_description": "To gain time on review answers", "reviews_automation_not_activated_title": "Activate automation", "reviews_average_response_time_too_high_description": "Google pushes the most reactive businesses", "reviews_average_response_time_too_high_title": "Answer each review in less than 72h", "reviews_hygiene_regularly_mentioned_in_negative_analysis_title": "Pay attention to your hygiene", "reviews_kitchen_regularly_mentioned_in_negative_analysis_title": "Pay attention to your food quality", "reviews_not_enough_description": "Contact us to learn how to gather more reviews", "reviews_not_enough_title": "You have not received enough reviews for the period", "reviews_price_regularly_mentioned_in_negative_analysis_title": "Pay attention to your prices", "reviews_quality_regularly_mentioned_in_negative_analysis_title": "Pay attention to your service quality", "reviews_service_regularly_mentioned_in_negative_analysis_title": "Pay attention to your service speed", "reviews_tag_description": "This element is mentioned in {{ value }}% of your negative reviews"}, "social_media": {"rs_hashtag_count_too_low_or_too_high_description": "Hashtags are part of your social media strategy", "rs_hashtag_count_too_low_or_too_high_title": "Add approx. 6 hashtags to your Instagram posts", "rs_no_account_in_inspiration_description": "To get inspired from social media top performers", "rs_no_account_in_inspiration_title": "Select accounts to follow", "rs_post_count_per_platform_too_low_description": "To reach up to 20% more appearances", "rs_post_count_per_platform_too_low_title": "Post 2 to 3 times a week on social media", "rs_posts_of_the_same_type_description": "Instagram prefers a mix between carousels and reels", "rs_posts_of_the_same_type_title": "Vary your social media post format", "rs_story_count_too_low_description": "Instagram looks up for regular accounts", "rs_story_count_too_low_title": "Post stories every day"}}, "short_title": "Tips", "subtitle": "To boost your earnings", "title": "<PERSON><PERSON><PERSON>", "try_again": "Please try again later."}}, "roles": {"add_user_btn": "Add user", "delete": {"cancel": "Cancel", "confirm": "Confirm", "text": "This will delete the user", "title": "Are you sure?"}, "existing_user": {"actions": {"cannot_add": "Cannot add businesses to this user", "cannot_remove": "Cannot remove businesses from this user", "cannot_update": "Cannot update user role for these businesses"}, "cannot_impersonate_admin": "You cannot connect to the account of another admin", "edit_user": "Edit user", "no_user_found": "No user found", "update_to_other_businesses": "Modify the management of businesses"}, "manager": {"actions": {"edit": "Edit", "pull_out": "Remove", "pull_out_from_all_my_locations": "From all my businesses", "pull_out_from_this_location": "From this businesses"}, "casl_role": "Role", "casl_role_tooltip": "A user can have a different role for each business", "email_header": "E-mail", "lastname": "Last name", "name_header": "First name", "no_organization": "This business is not attached to any organization, please contact <PERSON><PERSON> for more information.", "no_owner": "Adding a user is only available for businesses you own", "no_users": "No user found", "restaurant_name": "Business name", "restaurant_organization": "Business organization", "role_header": "Role", "user_organizations_header": "User organizations"}, "new_user": {"add_to_other_businesses": "Add to other businesses", "add_user": "Add user", "add_users": "Add users", "business": "Businesses", "cancel": "Cancel", "default_lang": "Default language", "email": "Email of user {{userNumber}}", "fill_required_fields": "Please fill in all required fields", "firstname": "First name", "lastname": "Last name", "role": "Role", "select_user": "Email", "validate": "Validate", "will_apply_to_all_restaurants": "Changes made to users will apply to all selected restaurants."}, "no_organization_for_restaurant": "This business is not bound to an organization.", "roles": {"downgrade_confirm": "You will remove privileges from yourself on this restaurant. \nYou will not be able to take them back without the help of another owner. \nAre you sure you want to apply the changes?", "editor": "Editor", "editor_subtext": "Can update business information, manage keywords and hashtags, and respond to reviews and comments.", "guest": "Reader", "guest_subtext": "Can view most information, but cannot edit it.", "moderator": "Moderator", "moderator_subtext": "Can respond to reviews and comments, and post on Google, Facebook, Instagram", "owner": "Owner", "owner_subtext": "Can modify the information of the business, platforms, manage users.", "pull_out_confirm": "the user will be permanently removed from the business", "pull_out_from_all_locations_confirm": "the user will be permanently removed from all the businesses", "role_updated": "Role updated", "user_removed": "The user has been successfully deleted"}, "users": "Users"}, "scan": {"default_description": "Hello, thank you for visiting {{ restaurantName }}. How was your experience?", "default_title": "Your opinion {{ restaurantName }}?", "description": "Hello, thank you for visiting {{ restaurantName }}. How was your experience?", "error": "Oops, something's gone wrong !", "redirection_in_progress": "Redirection ...", "scan_private_review": {"description": "We are sorry that your visit with us did not live up to your expectations. \nCould you take a few seconds to help us improve by explaining the circumstances of your visit. \n\nMany thanks 🙏", "error": "Oops, something's gone wrong!", "review_submitted": {"description": "We'll be looking at your feedback carefully! \nDon't hesitate to come back and tell us if we've improved :)", "title": "Thanks! 🙏"}, "submit": "Submit", "title": "What happened?"}, "title": "Your opinion on {{ restaurantName }}?"}, "semantic_analyses": {"go_to_detail_button": "See details"}, "send_ubereats_offer": {"none": "No", "ordinal_description": ", {{ordinal}} time", "send_offer": "Send an offer"}, "settings": {"are_you_sure": "Are you sure?", "business_remains_available": "The business remains available for other managers on the MalouApp", "business_settings": "Business settings", "confirm_delete": "The business <b>{restaurantName}} </b> will be deleted on the MalouApp along with its platforms, media and reviews for all managers.", "delete": "Delete", "delete_business": "Delete this business", "delete_restaurant_confirmed": "Business deleted", "delete_restaurant_verify": "The business will be deleted on the MalouApp as well as its platforms, photos and reviews for all the managers of the business", "delete_warning": "Warning! You will delete this business for all managers on the MalouApp", "dont_manage_business": "Cease to manage this business", "finished": "Confirm", "no": "No", "no_longer_manage": "Cease to manage", "status_not_ok": "Unknown error, please try later or contact customer service.", "unknown_error": "Unknown error", "yes": "Yes"}, "sidenav_content": {"admin": "Admin", "ai": "AI Assistant", "ai_and_automations": "AI & Automations", "automations": "Automations", "boosters": "Boosters", "boosters_presentation": "Presentation", "calendar": "Calendar", "campaigns": "Campaigns", "clients": "Customers", "comments": "Comments", "e_reputation": "E-Reputation", "gallery": "Gallery", "information": "Information", "inspirations": "Inspirations", "interactions": "Interactions", "keywords": "Keywords", "local_seo": "Local SEO", "message_templates": "Message templates", "messages": "Messages", "need_help": "Need help?", "platforms": "Platforms", "posts": "Posts", "resources": "Resources", "review_templates": "Review templates", "reviews": "Customer Reviews", "roi": "Earnings", "select_restaurant": {"all_businesses": "All my venues", "panel": {"add_business": "Add a business", "all_businesses": "All my venues"}}, "seo": "SEO", "settings": "Settings", "social_network": "Social Media", "statistics": "Insights", "store_locator": "Store locator", "stories": "Stories", "totems": "Totems", "users": "Users", "wheel_of_fortune": "Wheel of fortune"}, "single_post": {"legend": {"show_less_button_label": "see less", "show_more_button_label": "see more"}}, "snackbar": {"actions": {"dismiss": "<PERSON><PERSON><PERSON>"}, "dismiss": "OK"}, "social-posts": {"earlier_than_post": "Attention, the publication date has just been changed to a very close date. \nPlease check the publication date of your post.", "old_drafts_cannot_be_dragged": "Drafts with past dates cannot be moved.<br>Please edit it manually by clicking on the post.", "too_early": "You cannot move a post to a date earlier than the current date."}, "social-posts-list": {"select_posts_to_delete": "Select posts to delete them", "select_posts_to_duplicate": "Select posts to duplicate them"}, "social_post": {"author": "Created by {{author}}", "connect_google": "Connect Google", "delete_confirmation_modal": {"message": {"=1": "It will be definitely deleted on the Malouapp and you will no longer be able to recover it.", "other": "They will be definitely deleted on the Malouapp and you will no longer be able to recover them."}, "message_published_on_facebook": {"=1": "It will definitely be deleted on Facebook and the Malouapp and you will no longer be able to recover it.", "other": "They will definitely be deleted on Facebook and the Malouapp and you will no longer be able to recover them."}, "title": {"=1": "Are you sure you want to delete this post?", "other": "Are you sure you want to delete these posts?"}}, "delete_post": "Delete post", "duplicate_post": "Duplicate post", "edit_post": "Edit post", "feedback_count_tag": {"=1": "1 comment", "other": "# comments"}, "instagram_reel_without_videos_cant_be_duplicated": "Duplication unavailable because audio track contains copyright", "is_publishing": "Publishing", "new_feedback": "new commment(s)", "update_date": {"error": "An error occurred when modifying the date of publication", "success": "The post date of the post has been successfully modified"}, "view_post": "See the post"}, "social_post_item": {"publish_now_error": "Re-publication failed, please try again"}, "social_post_medias": {"add_media": "Add media", "drag_and_drop_here": "Drag and drop your files here", "max_medias_error": "You have reached the maximum number of media", "media_type_error_for_reel": "Please upload a video to create a Reel", "or_drag_and_drop": "or drag and drop your media here", "photo_or_video_file": "Photo and/or video file", "video_file": "Video file"}, "social_posts": {"Reel": "<PERSON><PERSON>", "add_post": "Create post", "add_reel": "Create a reel", "all": "All", "at": "at ", "cancel": "Cancel", "cannot_delete_ig_posts": "Unfortunately, it is not possible to delete an Instagram post already published from the MalouApp.", "combined_actions": "Bulk actions ({{ number }})", "create_draft": "Create a draft", "create_post": "Create post", "create_reel": "Create a reel", "current_month": "Current month", "current_year": "Current year", "custom": "Custom", "date": "Date", "delete": "Delete", "delete_post": "Delete post?", "delete_posts": "Delete posts?", "draft": "Draft", "duplicate_here_dialog": {"seo": {"title": "Duplicate to SEO"}}, "duplicate_in_seo": "Local SEO", "duplicate_in_social": "Social networks", "duplication_failed": "The duplication of the post seems to have failed.", "edit": "Edit", "error": "Warning", "error_delete_post": "An error occurred while deleting the post.", "error_delete_posts_bulk": "An error occurred when deleting certain posts.", "error_duplicate_post": "An error occurred when duplicating your post", "error_duplicate_posts_bulk": "An error occurred during the duplication of your posts", "error_occurred": "Oops, an error has occurred.", "error_swap_dates": "An error occurred while editing posts", "facebook_error": "We could not get data from Facebook/Instagram", "fatal_error": "Posting failed, please try again.", "feed": {"connect_instagram": "Connect Instagram", "instagram_not_connected": "Connect your Instagram account to view your feed"}, "feed_empty": "The Instagram feed is empty 😭", "feed_hidden": "Select Instagram in the filters to access the feed.", "feed_view": {"cancel_action": "Cancel your last action", "cant_go_back": "Unable to return to previous action", "cant_go_back_if_published": "Unable to return to the previous action, the post has already been published", "cant_switch_posts": "Posts could not be exchanged", "cant_update_post": "Post could not be updated", "create_post": "Create a new post", "edit": "Edit", "no_insta_post": "You don't have any Instagram posts, you can't access this page", "not_connected": "Instagram is not connected, you cannot access this page", "not_previous_date": "The date cannot be earlier than the current date", "post_already_published": "It is not possible to edit a published post", "save_anyway": "Save Anyway", "should_save_anyway": "Whoops! The selected date has already passed. Are you sure you want to save this draft on this date?", "title": "Feed view"}, "first_comment_not_posted": "The post was successfully published but an error occurred during the publication of the first comment", "format_not_supported": "This format is not supported", "go_to_mobile_app": {"back": "Back", "go_to_mobile_app": "Go to the Malou mobile app", "subtitle": "For better use in mobile format,", "subtitle2": "use the Malou app", "title": "Optimize your use of posts"}, "header": {"delete_bulk_not_allowed": "Posts published on Instagram and Mapstr cannot be deleted on the Malouapp", "max_10_posts_to_duplicate": "You cannot duplicate more than 10 posts at a time.", "select_post_to_delete_bulk": "Select a post to delete", "select_post_to_duplicate_bulk": "Select a post to duplicate"}, "improve_position": "We advise you to create posts to interact with your local customers and improve your rankings on your strategic keywords", "insta_posts_not_deleted": "Instagram posts cannot be deleted once posted.", "instagram_error": "An Instagram error has occurred", "instagram_error_tooltip": "Your post has been rescheduled automatically", "last_month": "Last month", "last_year": "Last year", "list_view": "List view", "max_number_of_media_reached": "You can add no more than {{max}} photos or videos", "media_error": "The media could not be downloaded by the platform. Please try again and contact the support team if the problem persists.", "new_social_post": {"accepted_formats": "Accepted formats: jpg, png, mp4, mov and quicktime", "accepted_formats_for_reel": "Accepted formats: mp4, mov and quicktime. Recommended format is 9:16.", "adapt_hashtag_list": "Adapt hashtag list with categories", "add_adapted_hashtags": "Add also a few hashtags adapted to the content (recipe, situation, specials, atmosphere...)", "add_caption": "Add a caption", "add_caption_mobile": "Caption", "add_hashtags": "Add hashtags", "add_hashtags_mobile": "Hashtags", "add_hashtags_modal": {"add_or_paste_hashtag": "Add or paste hashtags", "banned_hashtag": "The hashtag you are trying to add might be \"banned\" by Facebook or Instagram.", "close": "Close", "create": "Create", "empty_hashtag": "Empty hashtag", "enter_hashtag_text": "Please give a text for the hashtag you want to create", "hashtag_already_selected": "This hashtag has already been added to your list", "incorrect_hashtag": "Wrong hashtag", "learn_more": "Learn more", "multiple_hashtags": "{{last}} and {{count}} more", "no_punctuation": "Punctuation is not accepted, it will be automatically removed from the hashtag", "validate": "Confirm", "warning": "Warning!"}, "add_image": "Add an image to illustrate your post.", "add_location": "Add a location", "add_location_modal": {"add_location": "Add location", "search_location": "Find a location", "validate": "Confirm"}, "add_mapstr_cta_button_type": "Add a button", "add_mapstr_cta_text": "Add a link to the button", "add_media": "Add one or more medias (max: 10)", "add_media_for_reel": "Add one video to create a reel", "add_or_paste_hashtag": "Add or paste hashtags", "add_tag_modal": {"account_does_not_exist": "The account does not exist or is not a business account! \nPlease try again", "identification_not_allowed": "Instagram does not allow autocompletion or identification of personal accounts.", "search_account": "Find an account", "tag_account": "Tag an account", "unknown_error": "An error has occurred, our teams are trying to resolve it, please try again later", "validate": "Confirm"}, "add_text_edition": "Add text", "add_title": "Add a title", "ai_completion_error": "The generation with artificial intelligence has failed. \nTry Again", "ai_error": "The generation with artificial intelligence has failed. Try Again", "ai_hashtag_complete_hashtags": "Choose your hashtags to generate a list of hashtags suitable for this post", "ai_hashtag_complete_legend": "Complete the caption to generate a list of hashtags suitable for this post", "ai_hashtags_generate": "Generate a list of hashtags adapted to the post", "ai_placeholder": "our perfect chocolate cake to finish your meal", "ai_prompt_example": "Example: Generating a post about our new pistachio burrata dish with a formal tone", "ai_remaining_credits": "({{remainingCredits}} remaining credits)", "ai_remaining_credits_mobile": "({{remainingCredits}} credits)", "ask_ai": "Ask AI", "associate_post": "The post must be published on one platform at least", "associate_post_platform": "The post must be published on one platform at least", "autosave_state_error": "Changes could not be saved.", "autosave_state_saving": "Changes saved", "autosaved": "Automatically saved as draft", "back_to_editing": "Back to editing", "button_cta_only_for_mapstr": "The button only applies to the Mapstr platform", "cancel": "Cancel", "carousel_editor": {"delete": "Delete", "edit": "Edit", "tag": "Tag"}, "carousel_facebook_videos_error": "You cannot publish a carrousel containing video on Facebook", "changes_have_been_made": "Changes have been observed. Do you want to save this post as a draft?", "choose_one_platform": "Select at least one platform", "close": "Close", "copied": "Copied to the clipboard", "copy_hashtag": "Copy hashtags", "create_hashtags": "Create a hashtag", "date_error": "The date has passed.", "delete": "Delete", "delete_hashtags": "Delete all hashtags", "delete_media": "Delete media", "describe_your_caption": "About...", "dimensions_too_small": "Dimensions too small", "draft": "Save as draft", "draft_and_duplicate": "Save as draft & duplicate", "drag": "Drag here or upload a file", "duplicate": "Duplicate in the SEO part", "duplicate_in": "Duplicate in Local SEO", "edit_hashtags_modal": {"cancel": "Cancel", "confirm": "Yes, and do not show again", "error": "Error", "generate": "Generate", "generate_list": "Generate list of hashtags", "restart_selection": "You are about to change your hashtag selection. The hashtags already selected for this post will change. \nDo you want to continue?", "select_all": "Select all", "select_hashtags": "Select at least one category to generate a new list of hashtags!", "unknown_error": "An error has occurred, our teams are trying to resolve it, please try again later", "unselect_all": "Unselect all", "warning": "Warning!"}, "edit_or_delete_medias": "Edit or delete your media errors", "edit_pic": "Edit media", "edit_with_ai": "Edit with AI", "error": "Error", "error_media_too_big": "Media(s) too large", "error_too_many_medias": "Too many files", "error_wrong_format": "Invalid format", "errors": "error(s)", "fill_categories": "Configure your list of hashtags in your resources", "generate": "Generate", "happened_x_times": "media(s) had this issue.", "hashtags": "Hashtags", "hashtags_categories_placeholder": "Generate a list of hashtags with your categories", "hashtags_in_first_comment": "Hashtags in first comment", "hashtags_in_legend": "Hashtag in caption", "hashtags_limit": "You have reached the hashtag limit", "hashtags_number": "Between 3 and 6 hashtags", "hashtags_only_for_facebook_and_instagram": "The hashtags only concern the Facebook and Instagram platforms", "hashtags_recommendations": "Recommendations (AI)", "hashtags_score": "Hashtag usage score", "hashtags_type": "Type and location of your business", "ideas": "Ideas", "identify_account": "Identify an account", "identify_account_description": "Please enter the full account ID so you can tag it. \nInstagram does not allow us to identify personal accounts.", "image_analysis": {"add_image_to_use": "Add an image to use this feature", "cant_analyze_this_image": "Unable to analyze image", "cant_analyze_videos": "Unable to analyze videos", "waiting_for_image_analysis": "Waiting for image analysis"}, "image_too_heavy": "Warning, the media is too heavy, please change media or edit it before trying again(<8Mb)", "improve_ref": "The media is renamed with your keywords to improve your SEO ranking", "in_first_comment": "First comment", "in_legend": "In caption", "instagram": {"max_duration": "The video must be less than 60 seconds", "max_size_img": "Image size must be less than 8 MB", "max_size_video": "Video size must be less than 100 MB", "min_duration": "The video must be longer than 3 seconds"}, "interval": "Min 3 sec, max 60 sec", "interval_instagram": "Min 3 sec, max 60 sec.", "invalid_location": "The location of the post location is invalid or has been removed, please edit the post and change the location before reposting.", "invalid_media": "This media could not be uploaded. Encoding or format can be responsible. For videos, we recommend using videos in MPEG4-Part14 format.", "invalid_user_id": "Unable to load user with invalid private account or username", "large_file": "The media file is heavy, it might not be accepted", "later": "Schedule", "later_and_duplicate": "Schedule & duplicate", "limit_reached_error": "Reduce the number of hashtags, it should not exceed {{max_hashtags_number}}", "location": "Location", "location_only_for_facebook_and_instagram": "The location is only relevant to the Facebook and Instagram platforms", "main_hashtag_selected": "Brand hashtag", "mapstr_max_size_img": "Image size must be less than 4 MB", "mapstr_text_limit": "The text must not exceed 300 characters", "media_name": "Media name", "media_not_created": "The media could not be created", "minimum_dimensions_landscape": "Minimum dimensions for landscape format are 600x315", "minimum_dimensions_square": "Minimum dimensions for the square format are 600x600", "minimum_dimensions_vertical": "Minimum dimensions for the vertical format are 600x750", "mp4_recommended": "mp4 format is recommended", "must_start_with_https": "The URL must begin with https://", "my_gallery": "See media library", "no": "No", "no_image_and_mapstr_checked_error": "You must add at least one image to publish on Mapstr", "not_business": "Your account is not a Business account. Publishing via the MalouApp does not work with Creator or Personal accounts", "not_first_media_warning": "The media format must be the same as the first media in the carousel", "not_good": "Error", "not_only_one_image_and_only_mapstr_checked_error": "You must add only one image to publish on Mapstr", "now": "Publish now", "now_and_duplicate": "Publish now & duplicate", "only_one_media": "There can only be one media in a reel.", "open_feedbacks": "Display the comment tab ", "optimize": "Optimise", "optional": "(optional)", "or": "or", "out_of_proportion": "The media is out of proportion, please try to resize it or choose another media", "plan": "Later", "planned_post": "Planned post", "platform_not_connected": "This platform is not connected. Reconnect it in the Platform tab", "platforms": "Post on platforms", "post_in_progress": "Publishing in progress", "post_legend": "Caption", "post_on": "Publish on", "post_status_dialog": {"change_permissions": "Change permissions", "close": "Close", "edit_params": "Edit settings", "platform_connection": "your connection to {{ platform<PERSON>ey }} from the Platforms tab.", "post_created_comment_error": "Your {{postType}} was published but if there was a comment it was not published because permissions were missing. Please", "post_not_created": "your {{postType}} was not published due to missing permissions. Please", "post_type": {"post": "post", "reel": "reel"}, "publication_in_progress": "Publication in progress.", "status": {"creating": "Publishing in progress", "end_process": "End of process. No errors.", "error_step1": {"subtitle": "Your {{postType}} has not been published", "title": "Publishing failed"}, "error_step2": {"subtitle_facebook": "Your {{postType}} was published, but without the comment", "subtitle_instagram": "Your {{postType}} has not been published", "title": "Publishing failed"}, "publishing_facebook": "{{postType}} created. Retrieving information and comment if needed", "publishing_instagram": "{{postType}} created and published on Instagram"}}, "posts_not_completed": {"=1": " post with unresolved discussion", "other": " posts with unresolved discussions"}, "proposals": "Proposals", "publication_date": "Publishing date", "publication_error": "Publishing error", "publish": "Publish", "reconnect_fb": "Please reconnect Facebook or modify your access", "reconnect_platform": "Authorizations required, please reconnect the platforms (Facebook - Instagram) from the Platforms tab", "reconnect_retry": "Please reconnect the platform from the Platforms tab, and try again", "reconnect_with_auth": "Authorizations required, please reconnect the platform (Facebook - Instagram) from the Platforms tab, then click on 'Edit settings' and grant authorizations to the MalouApp", "reel_legend": "Reel caption", "reset_hashtags": "Remove hashtags from post", "restricted_account": "The account is restricted, please connect to instagram with the application and follow the instructions to reactivate the account. Then try again.", "save": "Save", "save_as_draft": "Draft", "saved_draft": "Saved draft", "seems_error": "It seems that there is at least one error in the media: <br>", "should_be_mp4": "Videos must be in mp4 format.", "should_be_png": "Images must be in png or jpeg format.", "should_be_video": "The media file must be MP4.", "should_we_save": "Save?", "show_in_feed": "Show the reel in the Instagram feed", "single_video_error_message": "To post a single video on Instagram, use the <PERSON><PERSON> post", "small_pic": "The image is too small, try to choose a larger one", "tag_account": "Tag an account", "textarea_loading_text": {"ai_can_translate": "AI can translate your text into", "ai_limit_reached": "AI usage limit reached", "ai_response_loading": "AI is thinking, taking into account", "emojis": "emojis", "lang_of_your_choice": "the language of your choice", "post_subject": "the subject of the post", "results_criteria": "results criteria", "usual_tone": "your usual tone"}, "there_is_error": "Error:", "tiktok_content_disclosure_error": "You need to indicate if your content promotes yourself, a third party, or both", "tiktok_options": {"content_disclosure_settings": {"branded_content": {"subtitle": "You are promoting another brand or third party", "title": "Branded Content", "tooltip": "The visibility for branded content can't be private (self only)"}, "branded_content_message": "Your photo/video will be labeled as 'Paid partnership'", "subtitle": "Tell others that this post promotes a brand, product or service", "tiktok_music_usage_policy": "By posting, you agree to TikTok's <a class=\"text-malou-color-primary malou-text-10--semibold\" href=\"https://www.tiktok.com/legal/page/global/music-usage-confirmation/en\" target=\"_blank\">Music Usage Confirmation</a>.", "tiktok_policy": "By posting, you agree to TikTok's <a class=\"text-malou-color-primary malou-text-10--semibold\" href=\"https://www.tiktok.com/legal/page/global/bc-policy/en\" target=\"_blank\">Branded Content Policy</a> and the <a class=\"text-malou-color \n-primary malou-text-10--semibold\" href=\"https://www.tiktok.com/legal/page/global/music-usage-confirmation/en\" target=\"_blank\">Music Usage Confirmation</a>.", "title": "Disclose post content", "your_brand": {"subtitle": "You are promoting for yourself or your business", "title": "Your brand"}, "your_brand_message": "Your photo/video will be labeled as 'Promotional content'"}, "interaction_ability": {"comment": "Comment", "duet": "Create a duo", "stitch": "Create a collage", "title": "Allow users to..."}, "tiktok_privacy_status": {"mutual_follow_friends": {"subtitle": "Followers you follow back"}, "self_only_disabled": "Branded content visibility cannot be set to private", "title": "Who can see this video?"}, "title": "TikTok Options"}, "timed_out": "The request to get the post from Instagram timed out, please try to synchronize the posts as it may have been published. Also, go to your Instagram page to see if the post is published to avoid publishing it again.", "title_only_for_mapstr": "The title only concerns the Mapstr platform", "translate_selection": "Translate selection", "unknown_error": "Unknown error", "unsupported_codec": "Encoding of your video is not supported, usually this problem occurs when you try to import a video from AirDrop, please convert your video to mp4.", "use_image_analysis": "Use the image to generate the caption", "write_here": "Write caption here", "write_here_or": "Write a caption here or", "write_legend_with_ai": "Generate a caption with AI", "write_message": "Write a message for your post", "write_with_ai": "Generate with AI", "yes": "Yes"}, "no_more_results": "You have reached the end of the list", "no_post": "You have no scheduled post", "no_posts": "Amstramgram is actually not that complicated", "no_posts_description": "You have no posts...", "no_posts_found": "The post doesn't pick up anything anymore!", "no_posts_found_description": "No posts match your search", "not_connected": "<PERSON><PERSON><PERSON><PERSON>, I no longer hear you. You hear me now? \nOh it happens to me all the time these days...", "not_connected_subtext": "Add an Instagram post to make your feed appear.", "not_for_video": "You cannot upload a video", "not_published": "Not published yet", "open_platform": "Open on platform", "pending": "Scheduled", "planned": "Scheduled", "platforms": "Platforms", "post_date_picker_modal": {"title": "Choose when to post"}, "post_for": "Why should you create posts?", "posts_deleted": "Posts have been deleted.", "posts_duplicated": "Your posts have been duplicated in the businesses you have selected", "previous_story_failed": "One of your stories preceding this one was not published. \nWe therefore prevented the sequel from being published.", "publication": "Post", "publication_in_progress": "In progress", "publish_error_platform_not_found": "The post could not be published; you may have deleted its platform between the time you scheduled this post and when you expected it to be published. Click \"Edit\" to republish your post.", "published": "Published", "ratio_error": "Media ratio error, try resizing", "reconnect_platform": "Please reconnect the social platforms and authorize the publication of content from the MalouApp.", "reel_or_tiktok": "Reel/Tiktok", "right_panel": {"feed_nav_title": "Feed", "feedback_nav_title": "Comments"}, "search": "Search", "select": "Select (0)", "show_old_drafts": "Show drafts with a date in the past", "show_tooltip": "To have a feed faithful to your Instagram, uncheck this option", "status": "Status", "success_delete_post": "The post has been deleted", "success_delete_posts_bulk": "The posts have been deleted", "success_duplicate_post": "Your post has been duplicated", "success_duplicate_posts_bulk": "Your posts have been duplicated", "sync": "Synchronize", "sync_in_progress": "Posts loading...", "transform_to_reel_modal": {"cancel": "Back to modification", "confirm": "Transform into Reel", "error": "An error occurred during the transformation of the post into <PERSON><PERSON>", "subtitle": "You cannot create a publication for Facebook and Instagram with a single video media.", "subtitle_facebook": "You cannot create a publication for Facebook with a single video media.", "subtitle_instagram": "You cannot create a publication for Instagram with a single video media.", "title": "Your post will be transformed into <PERSON><PERSON>"}, "unknown": "Issue", "unknown_error": "Unknown error", "unknown_error_long": "An error has occurred, our team are doing their best to fix it", "unknown_error_occurred": "A temporary error prevented the post from being published. \nYou can try again.", "update_post": {"error": "An error occurred when changing your post"}, "upsert_social_post_modal": {"content_form": {"caption": {"title": "Caption", "write_here_or": "Write your caption here or", "write_with_ai": "Create with AI"}, "collaborators": {"info": "If this account accepts the invitation, its username will be added as a co-author of the publication, which will be shared with its followers. It will also appear on their profile.", "subtitle": "(Full Instagram business account name required)", "title": "Instagram collaborators"}, "cta": {"errors": {"bad_url": "The URL must start with https://"}, "title": "Add a button"}, "media": {"title": "Media"}, "place": {"missing_fb_tooltip": "Connect Facebook to select a location", "title": "Location"}, "section_title": "Your post", "title": {"title": "Title"}, "user_tags": {"subtitle": "(Full Instagram business account name required)", "title": "Tag accounts"}}, "duplicate_and_select_platforms": {"duplicate": "Duplicate", "duplicate_hint": "Your post will be automatically optimized for SEO. You will then be able to edit it.", "mapstr_premium_not_connected": "If you are a Mapstr Premium subscriber, connect it in the Platforms tab by choosing \"Connect my Mapstr Premium account\".", "not_connected": "This platform is not connected. \nReconnect it in the Platforms tab.", "platforms": "Platforms"}, "errors": {"add_text": "Write a message for your post", "add_title": "Add a title", "caption_too_long": "The caption is too long (max 1500 characters)", "carousel_facebook_videos_error": "You can't post a carousel containing videos on Facebook", "edit_or_delete_medias": "Edit or delete your erroneous media", "mapstr_caption_too_long": "The caption is too long (max 300 characters)", "mapstr_title_too_long": "The caption is too long (max {{maxCharCount}} characters)", "no_image_and_mapstr_checked_error": "You must add at least one image to publish on Mapstr", "no_media_selected": "Add an image to illustrate your post", "no_platform_selected": "Select at least one platform", "not_only_one_image_and_only_mapstr_checked_error": "You must add only one image to publish on Mapstr", "planned_publication_date_in_past": "Publication date must be in the future", "single_video_error_message": "To post a single video on Instagram, use the <PERSON><PERSON> post", "tiktok_content_disclosure_error": "You need to indicate if your content promotes yourself, a third party, or both", "too_many_hashtags_error": "Reduce the number of hashtags, it should not exceed {{max_hashtags_number}}", "video_duration_error": "Video length must be between {{minDurationInSeconds}} and {{maxDurationInSeconds}} sec."}, "init_error": "An error occurred while initializing the post.", "previews_feed_notes": {"tabs": {"feed": {"title": "Feed"}, "notes": {"title": "Comments"}, "previews": {"facebook": {"comment": "Comment", "like": "Like", "likes": "<PERSON>, <PERSON><PERSON><PERSON><PERSON> and 52 other people", "location_name": "is at", "share": "Share", "write_a_comment": "Write a comment ..."}, "facebook_reel": {"follow": "Follow", "send": "Send"}, "instagram": {"likes": "467 likes", "tag_an_account": "Identify an account", "user_tag_tooltip": "Tag an account"}, "instagram_reel": {"follow": "Follow", "for_you": "For you", "people_commented": "<span class=\"font-bold\">{{username}}</span> and <span class=\"font-bold\">{{count}} other people</span> commented"}, "tag_account": {"description": "Complete name of the account required"}, "tiktok_reel": {"explore": "Explore", "following": "Following", "for_you": "For you"}, "title": "Preview"}}}, "title": {"create": "Create post", "create_reel": "Create a <PERSON>el", "create_reel_tiktok": "Create a Reel/Tik<PERSON>ok", "update": "Edit a post", "update_reel": "Edit a <PERSON>el", "update_reel_tiktok": "Modify a Reel/TikTok"}}, "view_by_feed": "View by feed Instagram", "view_by_post": "View by post", "want_delete_post": "Do you want to delete this post (it will also be deleted from the platforms on which it was published)?", "want_delete_posts": "Do you want to delete these posts? \nThey will also be deleted from the platforms on which they have been posted, except Instagram and Mapstr.", "warning_posts_moved": "You have moved items in your feed, check that the new dates correspond to your schedule.", "yes_delete": "Yes, delete"}, "star_with_number": {"without_review": "Without review"}, "statistics": {"common": {"compared_to": {"previous_period": "Compared with the previous period", "same_period_first_year": "Compared with the same period - first year with <PERSON><PERSON>", "since_start": "Compared with the starting period with malou"}, "current_period": "Current period", "download_statistics": "Download my insights", "download_statistics_sub_text": "All my insights", "previous_period": "Previous period", "same_period_first_year": "First year", "since_start": "Starting period"}, "e_reputation": {"platforms_ratings": {"marks": "Ratings", "no_data": "Data is not available !", "no_ratings": "It is possible that there is no ratings at this time\nor a recovery error has occurred.", "want_ratings_before_restaurant_creation": "We save this data from the first connection to the MalouApp"}, "reviews_analysis": {"contact_us": "Contact the Malou team to access the review analysis", "edit_filters": "Please modify the dates filter or try again later", "feeling": {"=0": "# {{sentiment}} sentiment", "=1": "# {{sentiment}} sentiment", "other": "# {{sentiment}} sentiments"}, "feelings": "sentiments", "negative": {"=0": "Negative", "=1": "Negative", "other": "Negative"}, "negative_sentiments": "Negative sentiments", "no_data_before_date": "Data unavailable before {{date}}", "on_analyzed_reviews": "On reviews with an analysis of their text", "on_total_reviews": "out of {{total}} reviews", "positive": {"=0": "Positive", "=1": "Positive", "other": "Positive"}, "positive_sentiments": "Positive sentiments", "redirection_warning": "(You will be redirected to the reviews page)", "related_review": "Related reviews", "related_verbatim": "Highlights related to the keyword", "review_with_sentiment": {"=0": "# reviews with {{sentiment}} sentiment", "=1": "# reviews with {{sentiment}} sentiment", "other": "# reviews with {{sentiment}} sentiments"}, "see_associated_reviews": "See the associated reviews", "see_detail": "view details", "semantic_analysis": "Semantic analysis of reviews", "semantic_analysis_disabled": "Semantic review analysis is not enabled", "semantic_analysis_not_available": "The semantic analysis of reviews is not available during this period", "show_review": "View review", "tag_evolution_of_semantic_analysis_of_reviews": "Semantic Analysis Evolution for Reviews", "tags_bar_chart_title": "Breakdown of sentiment by category", "top_topics": "Top topics", "top_topics_empty_state": "No subject has been adressed in this period", "top_topics_liked": "What your customers like", "top_topics_unliked": "What you need to improve", "topic_evolution": {"add_new_topic": "Add a new subject in the category “{{category}}”", "add_topic_advice_1": "1 to 2 words", "add_topic_advice_2": "Composed of names and/or adjectives", "add_topic_advice_3": "Not too vague (ex: avoid “food”) or too specific (ex: avoid “small cold pizza”)", "add_topic_advice_title": "Advice for better detection in reviews", "delete_topic": "Are you sure you want to delete the subject?", "error_adding_topic": "An error occurred when the topic was created", "error_empty_name": "The name of the topic cannot be empty", "error_name_already_exists": "A topic with this name already exists in the category \"{{category}}\"", "error_name_too_long": "The name of the subject must be less than {{length}} characters", "error_name_too_short": "The name of the subject must be greater than {{length}} characters", "negative": "Negative", "new": "New", "no_data_user_input_topic": "This subject added the {{date}} has not yet received an associated reviews", "no_negative_topic": {"see_my_positive_topics": "Look at my positive subjects", "subtitle": "You do not have a negative subject for the category {{ category }}!", "title": "Well done !"}, "no_positive_topic": {"see_my_negative_topics": "Look at my negative subjects", "subtitle": "You do not have a positive subject for the category {{ category }}.", "title": "Oops !"}, "no_topic_matching": "No topic matches your search.", "not_retroactive": "This addition is not retroactive on the analysis. \nOnly reviews received after the {{date}} may be associated with this new subject.", "positive": "Positive", "review_will_be_linked_to_other_topics": "The reviews related to this subject will be linked to other subjects appearing in your list within 24 hours maximum", "search_or_add": "Search or add a topic", "see_associated_reviews": "See associated reviews", "select_merge_name": "Select the name you want to give to your merged subject", "select_topic_name": "Enter the name of your subject", "the_topic_does_not_exist_html": "The subject \"<strong> {{topicText}} </strong>\" does not exist?", "title": "Evolution and details of your topics", "topics": "Topics"}, "topic_related_review": "Reviews associated with the topic \"{{ topic }}\"", "week_of": "Week of"}, "reviews_kpis": {"answer_rate": "Answer rate", "answer_rate_tooltip": "Calculated on reviews allowing an answer", "average_answer_time": "Response time", "average_reviews_rating": "Average review rating", "no_data": "Select another period or connect other platforms", "ratings": "Ratings", "restaurants": "Businesses", "reviews": "Reviews", "reviews_on_the_period": "Reviews on the period", "title": "Reviews for the selected period", "total_reviews": "{{ count }} in total on Google"}, "reviews_rating_calculator": {"goal_not_reached": {"need_reviews": "{{ numberOfReviewsToReachGoal }} reviews with 5 ⭐"}, "goal_reached": {"congratulations": "Congratulations", "your_rating": "Your note is {{ rating }}!"}, "maximum_stars": {"congratulations": "Congratulations, you have reached the stars!", "text": "5/5 we can't do better"}, "slider_description": "Select your target rating to see the number of reviews you need to reach your goal", "title": "Google Reviews calculator"}, "reviews_ratings": {"average_ratings_on_period": "Reviews split by rating", "no_reviews": "Change date or platform filters to display statistics", "reviews_on_period": "Reviews by platform", "selected_period": "Selected period", "some_platforms_got_no_data": "The following platforms do not have data for these dates:", "stars": {"=0": "star", "=1": "star", "other": "stars"}, "title": "Rating", "view_by": "View by", "week_of": "Week of", "without_rating": "Without ratings"}, "semantic_analysis": {"no_reviews_to_analyze": "No reviews found for semantic analysis"}, "tabs": {"reviews": "Reviews", "semantic_analysis": "Semantic Analysis"}}, "errors": {"change_filters": "Change filters", "change_period": "Change the time period to show results", "error_occurred_try_again": "An error has occurred, please try again later.", "gmb_not_connected": "You may not be logged in to Google anymore, try logging in again from the 'Platform' page", "no_data": "It seems there's no data to retrieve yet...🙈", "no_data_explained": "No data available for this time range.", "no_result": "Oh, it seems that you have no results over the period", "platforms_error": "The platforms are not well connected. \nTry reconnecting them from the Settings/Platforms tab. \n{{platforms}}", "platforms_not_connected": "Please connect at least one platform in the 'Platforms' tab", "requires_permissions": "Please reconnect {{platformName}} ensuring that you give all authorizations.", "select_at_least_one_totem": "Please select at least one totem", "server_is_not_responding": "The server does not respond...🙈"}, "seo": {"gmb_actions": {"actions": "Actions", "booking_click": "Click booking", "conversion_rate": "Conversion rate", "conversion_rate_tooltip": "The number of people who performed an action compared to the total number of appearances", "food_order_click": "Order", "itinerary_request": "Itinerary requests", "menu_click": "Click menu", "phone_call": "Phone calls", "user_actions_number_tooltip": "Actions that customers did on your listing", "view_by": "View by", "visit_website": "Website visits", "week_of": "Week of"}, "gmb_impressions": {"compared_to_previous_period": "Compared with the previous period", "gmb": "Google", "impressions": "Impressions", "impressions_maps": "Google Maps impressions", "impressions_search": "Google Search impressions", "info_gmb_data_take_time": "It can take a few days for Google insights to display. To show complete data, select a date range before {{date}}", "no_data_before_18_months": "We cannot retrieve Google data older than 18 months", "total_impressions": "Total Impressions", "total_impressions_tooltip": "The number of times your business appeared on a Google search", "view_by": "View by", "week_of": "Week of"}, "keyword_search_impressions": {"branding": "Notoriety", "branding_keywords": "Notoriety searches", "data_available_from": "Information available from {{date}}", "discovery": "Discovery", "discovery_keywords": "Discovery searches", "insights": {"branding_tooltip": "Number of <strong>Google</strong> searches that directly target your establishment (e.g. name of your establishment, brand, your chef ...)", "discovery_tooltip": "Generic <strong>Google </strong> searches that led to your page being discovered <strong> (e.g. \"best brunch\" or \"pancake paris\") </strong>", "title": "Google searches that led to your page"}, "missing_data": "Missing information for this month", "no_results": {"text": "Change the period to display results", "title": "Oh, it seems that you have no results in the period"}, "top_branding": "Top {{keywordsCount}} - Notoriety", "top_discovery": "Top {{keywordsCount}} - Discovery"}, "keywords": {"add_keywords": "Add my keywords", "advice_column": {"header_title": "Advice"}, "apparition_column": {"advice": "Advice", "change_keyword": "Change the keyword", "change_keyword_reason1": "● may serve different purposes (recipes, etc), not just searching for restaurants", "change_keyword_reason2": "● may be searched a lot in your regin/city but not around your restaurant", "change_keyword_subtext": "It's possible that this keyword: ", "header_title": "Appearances", "header_tooltip": "Estimated times your Google page appeared because of this keyword", "high_popularity_target_top_3_position": "For best results, target a position in the top 3 for this very popular keyword or change the keyword", "low_popularity_change_keyword": "This keyword is not searched enough by users. Change the keyword", "not_popular_enough_target_top_3_position": "For best results, target a position in the top 3 or change the keyword"}, "competitors_column": {"header_title": "Competitors"}, "content_created": "Content created", "content_created_tooltip": "You have:\n- Responded to {{ reviews }} reviews\n- Created {{ posts }} SEO posts", "evolution_column": {"cell": {"bad": "This keyword does not generate enough appearance.", "change_my_keyword": "Change my keyword", "evolution_tooltip": "Ranking of your business for \"{{text}}\"", "good": "This keyword creates more and more appearance. Continue to work it.", "wait": "Continue to work this keyword."}, "header_title": "Evolution"}, "first_keyword_tip_text": "Test a keyword for 3 months to measure its efficacity", "first_keyword_tip_title": "Leave time for results:", "in_top_20": "In the top 20", "in_top_20_tooltip": "Being on the first page allows you direct visibility with Internet users who search by need", "keywords": "Keywords", "keywords_column": {"header_title": "Keywords"}, "no_content_including_keywords": "You have not created content with a keyword score yet", "no_keywords_found": "You have not yet selected your keywords.", "no_keywords_found_description": "You can follow their positions, their developments and their appearances.", "only_malou_content": "Based on keywords created from the MalouApp", "popularity_column": {"header_title": "Popularity"}, "position_column": {"cell": {"no_info": "No information", "no_results_try_24_hours": "Google did not return any result for this search. - Please try again in 24 hours or change keyword", "no_results_try_other_period": "Google did not return any result for this search. Please try again over a different period of time.", "results_too_far_warning": "Attention: Some results are not in the correct location most likely due to temporary errors with Google."}, "header_title": "Google ranking", "header_tooltip": "We simulate localized searches within a radius of 2-3 km around your business."}, "score_average": "Average score", "score_average_tooltip": "The average rating of your keyword score across all created content", "second_keyword_tip_text": "Favor a keyword with medium popularity and target top 3 position. ", "second_keyword_tip_title": "Prioritize your visibility:", "your_keywords": "Your keywords"}, "tabs": {"gmb_visibility": "Google visibility", "keywords": "Keywords"}}, "social_network": {"errors": {"change_filters": "Please select platforms"}}, "social_networks": {"community": {"community": "Community", "followers": "Followers", "new_followers": "New followers", "view_by": "View by", "week_of": "Week of"}, "engagement": {"engagement": "Engagement", "engagement_detail": "Average on", "engagement_rate": "Engagement rate", "engagement_rate_details": "Interactions (likes + comments + shares + saved) / Followers / Number of posts", "impressions": "Impressions", "impressions_detail": "Total on", "impressions_details": "Total impressions of posts created over the period", "post": "post", "posts": "posts", "view_by": "View by", "week_of": "Week of"}, "kpis": {"engagement_rate": "Engagement rate", "followers": "Followers", "impressions": "Impressions", "posts": "Posts"}, "posts": {"carrousel_likes": "Instagram carrousel likes include likes on comments", "comments": "Comments", "created_at": "Created at", "engagement": "Engagement", "engagement_details": "Total interactions (likes, comments, shares and saves) on the post since its creation, divided by the number of followers on the creation date", "impressions": "Impressions", "likes": "<PERSON>s", "not_retrieved_for_facebook": "This data is not retrieved for Facebook", "platform": "Platform", "plays": "Plays", "posts": "Posts ", "reach": "Reach", "reach_details": "Total number of unique Instagram accounts that have seen the Reel", "reels": "<PERSON><PERSON> ", "saved": "Saved", "shares": "Shares"}, "posts_insights_table": {"top_3_posts_title": "Top 3 of your posts that reached peaks", "top_3_reels_title": "Top 3 of your reels that reached peaks"}, "stories": {"created_at": "Created the", "exits": "Exits", "exits_details": "Number of times a user quitted the story to go back to homepage", "impressions": "Impressions", "insights_not_available": "We have no data to display for this story", "less_than_five": "Less than 5", "no_stats_facebook_stories": "Data inaccessible for Facebook stories", "platform": "Platform", "reach": "Reach", "stories": "Stories ", "taps_back": "Previous story", "taps_back_details": "Number of times a user moved to the previous story", "taps_forward": "Next story", "taps_forward_details": "Number of times a user moved to the next story"}}, "totems": {"direct": "Direct", "estimated_review_count": {"estimated_review": "Totem reviews", "estimated_review_count": "Estimated number of reviews collected", "estimated_review_wheel_of_fortune": "Wheel of fortune reviews", "unknown_stars": "including {{ unknownStarsReviewCount }} review of unknown rating. \nTo know their rating, we recommend filtering the reviews when setting up"}, "five_stars": "5 stars", "four_stars": "4 stars", "no_booster_pack": "You are no longer subscribed to the Booster Pack", "one_star": "1 star", "private_review_count": {"private_review": "Private reviews", "private_review_count": "Number of private reviews", "title_tooltip": "Reviews that will not impact your rating on the platforms", "total": "Total"}, "scan_count": {"direct_scans": "Direct totems", "scan_count": "Scan count", "scans": {"=1": "scan", "other": "scans"}, "week_of": "Week of", "wheel_of_fortune": "Wheel of fortune"}, "three_stars": "3 stars", "totem": "Totem", "totems": "Totems", "two_stars": "2 stars", "unknown_star": "Unknown rating", "unknown_star_description": "Estimated number of reviews that could not be traced with certainty", "wheel_of_fortune": "Wheel of fortune"}, "wheel_of_fortune": {"estimated_review_count": {"estimated_review": "Estimated reviews", "title": "Estimated number of reviews collected"}, "gifts_distribution": {"business": "Business", "distribution_by_gift": "Distribution by gift", "gifts": "Gifts", "retrieved": "Retrieved", "retrieved_rate": "Retrieval rate", "winning": "Winners"}, "gifts_kpis": {"retrieved": "Gifts collected", "winning": "Winners"}}}, "statistics_pdf": {"boosters_pdf": {"title": "Booster insights"}, "common": {"restaurant": "Business: {{restaurants}}"}, "e_reputation_pdf": {"platforms": "Platforms: {{platforms}}", "title": "E-Reputation insights"}, "errors": {"missing_date_error": "The query params startDate and endDate are mandatory"}, "seo_pdf": {"title": "SEO insights"}, "social_network_pdf": {"title": "Social Network insights"}}, "status_filters": {"auto": "Automatic", "manual": "Manual", "pending": "Pending"}, "store_locator": {"edit": {"common": {"duplicate": "Duplicate everyone", "warning_message": "The layout will impact all your Locator blinds."}, "controls": {"cta": {"header": "Action buttons", "placeholder": "http://commande.com", "primary_button": "Main button", "secondary_button": "Secondary button", "title": "Button title"}, "image_upload": {"name": "Photo"}, "title": {"name": "Title", "placeholder": "Inform the name of your establishment and its location"}}, "location": {"selected": "Establishment concerned", "tooltip": "Your address, telephone number, timetables, as well as your characteristics are automatically extracted from your information. \nAny update will directly affect your Locator store."}, "style": {"background": "Bottom", "buttons": {"primary_background": "Primary button", "primary_border": "Primary button", "primary_text": "Primary button text", "title": "Buttons"}, "general": "General", "icon": "Icon", "text": "Text", "title": "Title"}, "tabs": {"content": "Content", "style": "Layout"}}}, "stories": {"cannot_delete_published_stories": "Unfortunately, it is not possible to delete a story already published from the MalouApp.", "carousel": {"no_current_stories": "No current Instagram story."}, "check_background": "Check that the chosen background meets your expectations", "create_story": "Create a story", "delete_stories": "Delete stories?", "duplication_failed": "The duplication of stories seems to have failed.", "edit_story": {"add_image_video": "Add an image or video to create your story.", "choose_one_platform": "Select at least one platform.", "edit_or_delete_medias": "Edit or delete your erroneous media.", "invalid_time": "Specify a time in the future.", "title": "Create a story"}, "new_story": {"form_header_title": "Stories"}, "no_stories_caption": "You have not yet created a story with <PERSON><PERSON>", "no_stories_headline": "A story to tell?", "no_story_found": "No story!", "no_story_found_caption": "...", "stories_deleted": "The stories have been successfully deleted.", "stories_duplicated": "Your stories have been duplicated in the businesses you have selected", "stories_list": {"no_platform": "You cannot create a story until one of the platforms is connected."}, "story": {"feedback_not_processed": "unresolved discussion.", "hours_remaining": "{{ hours }} hours remaining"}, "want_delete_stories": "Do you want to delete these stories? \nOnly draft or scheduled stories can be deleted."}, "story": {"media_too_long": "(Too long. Maximum: 60 sec)", "video": {"error": {"duration": "The duration must be between 3 and 60 sec.", "extension": "Only .mp4 and .mov extensions are accepted.", "height": "For Facebook, the video must be at least 960 pixels high.", "ratio": "The recommended ratio is 9:16. For Instagram it must be between 1:10 and 10:1.", "width": "The video must be a maximum of 1920 pixels wide."}}}, "template_info_modal": {"add_button": "Add a template", "description": "Thank you for your sample answer. \nHowever, we strongly recommend that you create at least two unhappy review response templates with comments to make it look more natural!", "ignore_button": "Ignore", "neutral": "Neutral Reviews", "satisfied": "Satisfied reviews", "title": "Add a second response template!", "unhappy": "Unhappy Reviews", "with_comment": "With commentary", "with_or_without": "With or without comment", "without_comment": "No comment", "without_rating": "Review without note"}, "templates": {"auto_replies_disabled": "Automatic replies disabled", "auto_replies_enabled": "Automatic replies enabled", "auto_reviews": {"auto_warning_message": "Attention! \nWe strongly advise against enabling automatic response for reviews with comments and reviews from dissatisfied customers.", "auto_warning_message_with_or_without": "Attention! \nWe strongly advise against enabling automatic response for reviews with comments.", "auto_warning_message_without": "Attention! \nWe strongly advise against turning on auto-reply for dissatisfied Customer Reviews", "off": "Auto answer off", "on": "Auto answer on"}, "auto_tooltip": "To disable auto-response, please select all relevant templates and disable automation in \"Bulk Actions\"", "auto_tooltip_on": "To enable auto-response, please select all relevant templates and enable automation in \"Bulk Actions\"", "automation": {"update_active": {"text": "Chosen models will be automated for all auto-response eligible platforms if they do not have a platform already selected", "text_with_pending": "<p>Are you sure you want to automate these review templates? We advise you to re-read each of the models carefully to ensure their consistency</p>  <p style=\"color:red\">Please note that draft models cannot be automated.</p>", "title": "Automate selected templates?"}}, "cancel": "Cancel", "category": "Category", "change_filters": "No templates found. Please change your filters.", "change_keywords": "Please edit keywords before using it.", "combined_actions": "Bulk actions ({{ number }})", "comment": "Comment", "comment_template": "Social media comments", "comments": "Comments", "copy_prefix": "Copy of {{ name }}", "create": {"address": "@Address", "business_name": "@Business Name", "cancel": "Cancel", "choose_language": "Choose language", "client_name": "@Customer name", "close": "Close", "confirm": "Confirm", "create": "Create", "create_wording": "Example: Service, food, quality", "menu_link": "@Menu", "no_intro_formula": "Do not use an introduction or signature formula: they will be added automatically", "optional": "(optional)", "phone": "@Phone", "placeholder": "Example :\n\n\nHello @Customer Name,\n\n\nThanks for your review....\n\n\n@Business name team", "ratings": ["Neutral customer (3 stars)", "Satisfied customer (4-5 stars)", "Unsatisfied customer (1-2 stars)"], "regular_hours": "@Opening time", "template_already_exist": "A response template already exists with this label.", "template_created": "Template created", "template_text": "Response template text", "template_updated": "Template updated", "update": "Update", "user_name": "@My Name", "website": "@Website", "which_language": "This model is in which language?", "which_review_type": "Review types concerned by this template", "which_wording": "Review template title", "with_comment": "Reviews with comments", "with_or_without_comment": "Reviews with or without comments", "without_comment": "Reviews without comments", "wording_ex": "eg: good value for money, unpleasant waiter, wait time for a table...", "write_content": "Write the content of the response template"}, "create_response_template": "Create a template", "create_template": "Create a template", "created_success": "Response template created successfully", "delete_prompt": {"=0": "Error, this template doesn't exist", "=1": "Do you want to delete this template?", "other": "Do you want to delete these templates?"}, "delete_success": {"=1": "The template has been deleted", "other": "The templates have been deleted"}, "disable_automation": "Disable automation", "draft": "Draft", "duplicate": "Duplicate", "duplicate_prompt": {"=0": "Error, this template doesn't exist", "=1": "Do you want to duplicate this template?", "other": "Do you want to duplicate these templates?"}, "duplicate_success": {"=1": "The template has been duplicated", "other": "The templates have been duplicated"}, "duplication_failed": "Duplication failed", "duplication_succeeded": "Successful duplication", "edit": "Edit", "edit_create_template": {"automation": {"description": "Use this template to automatically respond to reviews from the platforms below:", "platforms_select_title": "Platforms", "title": "Auto reply", "toggle_title": "Make this template reply automatically"}, "confirm": "Confirm anyway", "filter_by_notes": "Filter by rating", "no_platform_selected": "Select at least one platform", "text_has_variable_to_replace": "Please replace the texts between <<>> with keywords"}, "enable_automation": "Activate automation", "error": "Error", "here": "here", "keywords": {"cancel": "Cancel", "confirm": "Choose my keywords", "text": "Before creating your templates, we strongly recommend that you choose your keywords in order to optimize them", "title": "You haven't defined your keywords yet"}, "language": "Language", "message": {"actions": {"create_template": "Create a template", "duplicate_here": "In this list", "must_select_template": "Select at least one template", "template": "Message templates"}, "create_template": "Create a message template", "no_templates": {"add_template": "Add a message template", "subtitle": "Respond quickly to your messages with templates", "title": "Create message templates to gain more time"}, "no_templates_with_filters": {"title": "No template matches your search"}, "select_all": "Select all templates", "upsert_modal": {"template_name_placeholder": "Example: no reservations, opening hours"}}, "message_template": "Messages", "messages_templates": "Message templates", "models_will_be_deleted": "Templates <b>{{templatesNames}}</b> will be permanently deleted", "neutral": "Neutral customer", "no_response_template": "You have not created any template yet", "other_restaurants": "to other businesses", "platforms": "Platforms", "rating": "Rating", "ratings": {"=0": "Review without rating", "=1": "1 star review", "other": "# star review"}, "response_template_for": "Why should you create templates?", "review": {"actions": {"back_to_draft": "Go back to draft", "create_template": "Create a template", "duplicate_here": "In this list", "must_select_template": "Select at least one template", "templates": "Review templates"}, "auto_replies": "Automated", "auto_reply": "Automatic reply", "automated_tooltip": {"default_text": "This template is used in an automation for reviews", "rating_difference": "{{rating}} stars", "with_comment_difference": {"with": "with comment", "without": "without comment"}}, "create_template": "Create a review template", "filters": {"automated": "Automated models", "comments": "Comments", "name": "Label", "ratings": "Rating", "text": "Text", "withText": "With comment", "withoutText": "Without comment"}, "generation_failed": "An error occurred while generating the models. \nTry Again.", "no_templates": {"add_english_templates": "English", "add_french_and_english_templates": "French and English", "add_french_templates": "French", "add_template": "Add review template", "subtitle": "We prepared a few suggestions for you. In which language would you like to see them?", "title": "Find your templates here to answer reviews"}, "no_templates_with_filters": {"title": "No template matches your search"}, "upsert_modal": {"template_name": "Message template title", "template_name_placeholder": "Title"}, "with_comment": "Review with comment", "with_comment_short": "With", "with_or_without": "Review with or without comment", "with_or_without_short": "With or without", "without_comment": "Review without comment", "without_comment_short": "Without"}, "review_template": "Review response", "review_type": "Review type", "reviews_templates": "Review templates", "satisfied": "Satisfied customer", "search": "Search", "select": "Select (0)", "status": "Status", "status_not_ok": "Unknown error, please try again later or contact customer service", "table": {"auto_header": "Automatic"}, "template_row": {"message": "Message", "review": "Review"}, "template_will_be_deleted": "The <b>{{templateName}}</b> template will be permanently deleted", "templates_duplicated": "Your response templates have been duplicated in the businesses you have selected", "text": "Text", "to_be_confirmed": "This template was duplicated from {{restaurant}} by {{user}}.", "to_be_confirmed_no_detail": "This template has been duplicated.", "to_be_confirmed_restaurant_only": "This template has been duplicated from {{restaurant}}.", "to_be_confirmed_user_only": "This template has been duplicated by {{user}}.", "type": "Type", "types": {"message": "Message", "review": "Review"}, "unknown_error": "Unknown error", "unsatisfied": "Unsatisfied customer", "update": "Updated!", "variables": {"address": "@Address", "business_name": "@Business Name", "client_name": "@Customer name", "menu_link": "@Menu", "my_firstname": "@My Name", "phone": "@Phone", "regular_hours": "@Opening time", "website": "@Website"}, "wording": "Title", "yes_delete": "Yes, delete"}, "top_post_card": {"engagement": "Engagement", "impressions": "Impressions"}, "unsubscribe": {"error": "Unknown error", "error_detail": "An error occurred, we could not unsubscribe you from the communications of the restaurant {{restaurantName}}", "unsubscribed": "Successful unsubscribe", "unsubscribed_details": "You are successfully unsubscribed to communications from the restaurant {{restaurantName}}"}, "upload_image": {"could_not_load_file": "Unable to load this file.", "invalid_dimensions": "Media format is not accepted, length and width must not exceed 180: length {{ width }}, width {{ height }}", "invalid_format": "The media format is not accepted", "invalid_image": "Unaccepted media", "invalid_size": "Media format is not accepted: {{ size }}", "no_file": "Media not found"}, "user": {"profile": {"edit": "Edit my profile"}, "reports-settings": {"add_recipients": "Add a recipient", "add_recipients_to_activate": "Add recipients to enable this report.", "add_restaurants_to_activate": "Add businesses to enable this report.", "daily-reviews-report": "Daily review report", "daily-reviews-report-send-date": "Receive every morning", "invalid_email": "<PERSON><PERSON><PERSON>", "invalid_form": "Review report configurations contain errors.", "monthly-performance-report": "Monthly performance report", "monthly-performance-report-send-date": "Receive every 5th of the month", "performance-reports": "Performance Report", "performance-reports-subtitle": "Receive an email summary of the main statistics for the selected businesses", "reviews-reports": "Review report", "reviews-reports-subtitle": "Receive an email that recaps the Customer Reviews you received for the selected businesses", "successfully_saved": "The advisory reports have been successfully recorded", "test_report_error": "Error sending report for this configuration", "test_report_sent": "Report for this configuration sent successfully", "weekly-performance-report": "Weekly performance report", "weekly-performance-report-send-date": "Receive every Monday", "weekly-reviews-report": "Weekly review report", "weekly-reviews-report-send-date": "Receive every Monday"}}, "validate_credentials": {"choose_organization_title": "You would like to associate this access to which organisation?", "confirm": "Confirm", "no_organization_found": "If you don't see any organizations, please contact an admin at Malou.", "organization_limit_reached": "The limit of businesses has been reached for this organization", "page_title": "Access confirmation"}, "validation": {"modal": {"invalid_keyword_text": "New keywords must be between 3 and 80 characters, with no more than 10 words. ", "my_keywords": "Your keywords shortlist ({{number}}/10)", "propositions": "Proposals", "subtitle_right": "<b>Choose up to 10 keywords from the list on the left</b> that you want to use to improve your local SEO", "title_left": "Choose your keywords", "title_right": "Your selection of keywords to work on ({{number}}/10)"}}, "website": "Website", "wheel_of_fortune": {"create_wheel_card": {"all_already_have_wof": "All your businesses have their own wheel of fortune. \nTo create a new one, remove the businesses from the other wheels.", "create_aggregated": "Create an aggregated wheel of fortune", "create_single": "Create my wheel of fortune", "description": "Maximize reviews, build customer loyalty and grow your database with our wheel of fortune.\n \nEasily combine it with your totems.", "how_does_it_work": "How does it work?", "more_info": "More information", "rules_details": {"step1": "1. Your customers <b>scan the totem</b> to participate in our wheel of fortune", "step2": "2. By leaving a <b>review on Google</b> or by <b>subscribing on Instagram</b>, your customers can spin the wheel", "step3": "3. After spinning the wheel, your customers win a gift from the <b>prizes you have chosen</b>", "step4": "4. Your customers enter their <b>personal information</b> (first name, email address, telephone number) to receive their gift immediately or later", "step5": "5. After collecting their gift, your customers confirm receipt with you by clicking on “<b>I collected my gift</b>”"}}, "create_wheel_of_fortune": "Wheel of fortune", "gift_out_of_stock": {"change_stocks": "Edit inventory", "change_stocks_to_offer_gift": "If you still want to offer this gift to your customers, modify the stocks.", "out_of_stock": "Please note, your gift \"{{name}}\" is out of stock."}, "new_wheel_modal": {"admin_warning": "As a <PERSON><PERSON> admin, check that your selected businesses are connected to the chosen redirection platform (Google, TripAdvisor, etc.).", "create_aggregated_or_restaurant_wheel_modal": {"create_for_all": "Create for everyone", "create_for_this_location": "Create for this business", "did_you_know": "Did you know that you can create a wheel of fortune for all your businesses?", "do_not_show_again": "Do not show again", "go_to_all_locations": "Go to the resources page of “all my businesses."}, "errors": {"are_you_sure": "Are you sure you want to remove your Wheel of Fortune?", "create_error": "An error occurred while creating the wheel of fortune", "error_getting_totems": "An error occurred while retrieving the totems", "gift_weight_sum_not_100": "The sum of the winning probabilities must be 100.", "gift_with_too_long_name": "One of the gifts has a name exceeding the number of characters allowed", "gift_without_name": "One of the gifts has no name", "gift_without_probability": "One of the gifts has no win probability", "has_error": "Missing information: {{tabs}}", "has_multiple_times_the_same_redirection_platform": "You cannot have multiple redirects to the same platform", "missing_colors": "Select two colors for the wheel", "missing_end_date": "Select a valid end date", "missing_redirection_platform": "Select a redirect platform", "missing_start_date": "Select a valid start date", "need_at_least_one": "Select at least one restaurant", "no_restaurant_selected": "No business selected", "update_error": "An error occurred while editing the wheel of fortune", "wont_be_part_of_aggregated": "It will no longer be part of the aggregate wheel but you will be able to recreate a single one."}, "preview": "Preview", "tabs": {"appearance": {"add_logo": "Add your logo", "add_logo_for_all_restaurants": "Add your logo for all restaurants", "color_choice": "Choice of colors", "default_logo": "By default, the logo will be the one indicated on the information page of each restaurant", "drop_logo": "Submit your logo", "errors": {"error_uploading_file": "An error occurred while downloading the logo", "file_too_large": "Your file does not match the required dimensions", "format_not_accepted": "Your file must be in png, jpeg or svg format"}, "file": "File", "importing": "currently importing", "primary_color": "Primary color", "secondary_color": "Secondary color", "title": "Appearance", "upload_file": {"accepted_files": "50 MB maximum - png, jpeg, svg", "click_to_download": "Click to download", "or_drag_drop": "or drag and drop your logo"}}, "gifts": {"default_values": {"5_percent_discount": "5% discount 🤩", "appetizer": "An appetizer 🫒", "coffee": "A coffee ☕️", "dessert": "A dessert 🍰", "glass_of_wine": "A glass of wine 🍷", "lunch_menu": "A lunch menu 🍽️", "t_shirt": "A T-shirt 🎽"}, "edit_gift_stocks_modal": {"copy_stocks": "Copy stock", "current_stock": "Current stock", "quantity_input": {"placeholder": "Eg: 10", "title": "Edit stock", "unlimited": "unlimited"}, "tabs": {"conditions": {"placeholder": "Ex: Valid only for the lunch menu", "subtitle": "The conditions will be displayed in the gift email to the player.", "title": "Terms"}, "stocks": {"title": "Change stocks"}}, "title": "Advanced options: {{gift}}", "unlimited_stocks": "Unlimited stocks", "your_establishments": "Your businesses"}, "gift_card": {"change_stocks": "Advanced options", "name_input": {"placeholder": "Eg: <PERSON><PERSON><PERSON>", "title": "Gift name"}, "title": "Gift {{index}}", "weight_input": {"placeholder": "Ex: 20.5", "title": "Win probability"}, "win_probability": {"percentage": "{{value}}%"}}, "subtitle": "({{maxCount}} maximum)", "title": "Choice of gifts"}, "global_settings": {"connect_more_platforms": "Connect your platforms to add more redirection options.", "connect_more_platforms_1": "Connect your platforms from", "connect_more_platforms_2": "to add more redirection options.", "connect_more_platforms_cta": "the Settings tab", "gift_claim_duration_in_days": {"gift_claim_duration_in_days_option": "{{count}} days", "one_month": "1 month", "six_month": "6 months", "subtitle": "A reminder email will be sent 5 days before they can no longer collect their gift.", "title": "Gift Collection Validity Period", "two_month": "2 months"}, "gift_claim_start_date_option": {"make_your_clients_come_back": "(Encourage your customers to come back)", "title": "Gift Collection Period"}, "redirection_platform": {"choose_your_redirection": "Choose your redirection", "your_redirection": "Your redirection"}, "redirection_settings": {"nextDrawEnabledDelay": "Allow customers to replay", "nth_redirection": {"=1": "1st redirection", "=2": "2nd redirection", "=3": "3rd redirection", "other": "{{count}}th redirection"}, "player_can_play_after_24_hours_and_wont_be_redirected_nth_time": "From the {{nthTime}}th time, your customers will be able to continue playing every 24 hours without being redirected", "player_can_play_after_28_hours_and_wont_be_redirected_nth_time": "From the {{nthTime}}th time, your customers will be able to continue playing every 48 hours without being redirected", "player_can_play_after_one week_and_wont_be_redirected_nth_time": "From the {{nthTime}}th time, your customers will be able to continue playing every week without being redirected", "player_wont_be_redirected_nth_time": "From the {{nthTime}}th time, your customers will be able to continue playing without being redirected", "redirection": "Redirect", "should_redirect": {"no_redirection": "No redirection", "redirect_to": "Redirect the player to...", "title": "Choose your redirects"}, "title": "Redirection strategy"}, "wheel_of_fortune_dates": {"end_date": "End date", "no_end_date": "I don't want an end date", "start_date": "Start date", "title": "Dates of your game"}}, "parameters": "Settings", "restaurants": {"all_totems": "All totems", "connect_gmb_1": "Connect {{<PERSON><PERSON>ey}} in the", "connect_gmb_2": "of the business to be able to select it in your wheel of fortune", "has_aggregated_wheel_1": "This business is already part of a grouped wheel of fortune, you must", "has_aggregated_wheel_2": "to select it here.", "has_aggregated_wheel_cta": "deactivate it from its group", "has_single_wheel_1": "This business already has its own wheel of fortune, you can't have two for the same location. Go to the", "has_single_wheel_2": "for this business to deactivate the wheel of fortune and activate it here.", "has_single_wheel_cta": "Boosters page", "locked_restaurant": "Limited access", "no_booster_pack_1": "This business does not subscribe to the Booster pack,", "no_booster_pack_2": "click here", "no_booster_pack_3": "to send us a subscription request.", "no_totem": "No totem", "no_totem_selected": "No totem selected", "select_all": "Select all", "settings_page": "settings page", "title": "Businesses", "totems": "totems", "which_restaurant": "Which business(s) would you like to attach your wheel of fortune to?", "which_totems": "Totems Connected", "will_redirect": "The chip and QR code will redirect to this wheel of fortune."}}, "update": "Wheel of fortune settings"}, "subscription_request_modal": {"contact_me": "Get more information", "feature_part_of_booster_pack": "This feature is part of the <strong>Booster Pack</strong>.", "gain_reviews_and_subscribers": "Make your <strong>reviews and subscribers take off</strong> thanks to the Booster Pack with:", "get_3_to_6_times_more_reviews": "Our customers subscribing to the Booster Pack gain on average between <strong>3 and 6 times more reviews</strong> than before.", "nfc_items": "3 supports with <strong>contactless NFC</strong> technology", "personalized_advices": "Personalized <strong>advice</strong>", "request_sent": "Your request has been taken into account", "request_successfully_sent": "Request sent, you will be contacted by our team.", "stickers": "<strong>QR Code stickers</strong> redirecting to the link of your choice", "title": "Information", "wheel_of_fortune": "Access to the <strong>Wheel of Fortune</strong> feature"}, "wheel_of_fortune": "Wheel of fortune {{count}}", "wheel_of_fortune_card": {"actions": {"can_not_delete": "Limited access, you cannot delete this wheel of fortune", "copy_link": "Copy wheel link", "delete": "Delete", "download_post": {"landscape": "A4 Landscape", "portrait": "A4 Portrait", "title": "Download the poster"}, "download_qr_code": "Download the QR code"}, "are_you_sure_delete": "Are you sure you want to delete the aggregate wheel of fortune?", "are_you_sure_delete_restaurant_wheel": "Are you sure you want to delete the wheel of fortune?", "delete_will_be_for_all": "The action will be effective for all businesses associated with the wheel of fortune.", "details": {"gifts": "Gifts", "restaurants": {"limited_access": "Limited access, you can only modify stocks", "title": "Businesses concerned"}, "settings": {"duration_for_retrieval": "Gift Collection Validity Period", "email_retrieval": "A reminder email will be sent 5 days before they can no longer collect their gift.", "start_retrieval_date": "When can the winners collect their gifts?", "title": "Settings"}}, "empty_stock": "One of your gifts is out of stock. \nChange your advanced options.", "errors": {"could_not_copy": "An error occurred while copying the wheel link", "could_not_delete": "An error occurred while deleting wheel of fortune"}, "limited_aggregated_wheel_of_fortune": "Modification of your wheel is limited to stock because you are part of a wheel aggregated with other businesses.", "link_copied": "The link has been copied to the clipboard", "restaurant_wheel_of_fortune_title": "Wheel of Fortune", "wheel_delete": "The wheel of fortune has been removed"}, "wheel_of_fortune_poster": {"discover_gift": "discover your gift!", "turn_the_wheel": "Spin the wheel of fortune and", "winner": "100% winner!"}}}