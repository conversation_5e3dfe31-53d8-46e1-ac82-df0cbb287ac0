import { NextFunction, Request, Response, Router } from 'express';
import { singleton } from 'tsyringe';

import {
    ActivateYextParamsDto,
    AddRestaurantForUserWithEmailBodyDto,
    AddRestaurantForUserWithEmailParamsDto,
    AddRestaurantsForUserWithEmailBodyDto,
    AddRestaurantsForUserWithEmailParamsDto,
    CheckRestaurantIdInParamsDto,
    CreatePlatformAccessBodyDto,
    CreatePlatformAccessParamsDto,
    DeactivateYextParamsDto,
    DuplicateSpecialHoursBodyDto,
    GetRestaurantsForUserWithEmailParamsDto,
    HandleShowPlatformAccessParamsDto,
    HandleValidatePlatformAccessBodyDto,
    HandleValidatePlatformAccessParamsDto,
    UpdatePlatformAccessStatusBodyDto,
    UpdatePlatformAccessStatusParamsDto,
    UpdateRestaurantActiveBodyDto,
    UpdateRestaurantBodyDto,
    UpdateRestaurantParamsDto,
    updateRestaurantsForUserWithEmailBodyDto,
    updateRestaurantsForUserWithEmailParamsDto,
} from '@malou-io/package-dto';
import { Role } from '@malou-io/package-utils';

import AbstractRouter from ':helpers/abstracts/abstract-router';
import { casl, userHasAccessToRestaurant } from ':helpers/casl/middlewares';
import { RequestWithPermissions } from ':helpers/utils.types';
import RestaurantsController from ':modules/restaurants/restaurants.controller';
import { isRestaurantActive } from ':modules/restaurants/restaurants.middlewares';
import { authorize } from ':plugins/passport';

@singleton()
export default class RestaurantsRouter extends AbstractRouter {
    constructor(private _restaurantsController: RestaurantsController) {
        super();
    }

    init(): Router {
        /**
         * Get all the restaurants the user is allowed to access.
         */
        this.router.get('/', authorize([Role.MALOU_FREE]), (req, res, next) =>
            this._restaurantsController.handleGetRestaurantsForUser(req, res, next)
        );

        this.router.get('/search', (req: any, res, next) => this._restaurantsController.handleSearchRestaurants(req, res, next));

        this.router.get('/searchV2', authorize(), (req: any, res, next) =>
            this._restaurantsController.handleSearchRestaurantsV2(req, res, next)
        );

        this.router.get('/from-provider', authorize([Role.OWNER, Role.ADMIN]), (req: any, res, next) =>
            this._restaurantsController.handleGetRestaurantsFromProvider(req, res, next)
        );

        this.router.post(
            '/:restaurant_id/special-hours/duplicate',
            authorize(),
            casl(),
            (req: Request<CheckRestaurantIdInParamsDto, never, DuplicateSpecialHoursBodyDto>, res, next) =>
                this._restaurantsController.handleDuplicateSpecialHours(req, res, next)
        );

        this.router.get('/without-sticker', (req: any, res, next) =>
            this._restaurantsController.handleGetRestaurantsWithoutSticker(req, res, next)
        );

        this.router.get('/all', authorize([Role.ADMIN]), (req, res, next) =>
            this._restaurantsController.handleGetAllRestaurants(req, res, next)
        );

        this.router.get('/ids', authorize(), (req, res, next) => this._restaurantsController.handleGetRestaurantsByIds(req, res, next));

        this.router.get(
            '/many/users/:user_id',
            authorize(),
            casl(),
            (req: RequestWithPermissions<GetRestaurantsForUserWithEmailParamsDto>, res, next) =>
                this._restaurantsController.handleGetRestaurantsForUserWithEmail(req, res, next)
        );

        this.router.post(
            '/many/users/:user_id/add',
            authorize(),
            casl(),
            (req: RequestWithPermissions<AddRestaurantsForUserWithEmailParamsDto, any, AddRestaurantsForUserWithEmailBodyDto>, res, next) =>
                this._restaurantsController.handleAddRestaurantsForUserWithEmail(req, res, next)
        );

        this.router.post(
            '/many/users/:user_id/update',
            authorize(),
            casl(),
            (
                req: RequestWithPermissions<updateRestaurantsForUserWithEmailParamsDto, any, updateRestaurantsForUserWithEmailBodyDto>,
                res,
                next
            ) => this._restaurantsController.handleUpdateRestaurantsForUserWithEmail(req, res, next)
        );
        /**
         *
         * Keep this route in api vs service-users to keep service-users clean
         * @api {get} /restaurants/all Get all restaurants
         * @apiName AddRestaurantForUser
         * @apiGroup Restaurant
         *
         * @apiParam {string} restaurant_id Restaurant's unique ID.
         * @apiParam {string} api_endpoint Google api endpoint.
         * @apiParamExample {json}
         * {
         *    restaurant_id: '1234',
         *    api_endpoint: 'account/xxxxx/location/xxxxx'
         * }
         * @apiSuccess {Object}
         *
         * @apiSuccessExample Success-Response:
         *     HTTP/1.1 200 OK
         *     {
         *       "msg": "Success"
         *     }
         *
         * @apiError Bad Request, need restaurant_id.
         * @apiErrorExample {json} Error-Response:
         *     HTTP/1.1 400 Bad Request
         *     {
         *       "msg": {
         *            error: true,
         *            message: 'missing_param'
         *            errorData: 'xxxx'
         *        }
         *     }
         */
        this.router.get('/:restaurant_id/user/add', authorize([Role.ADMIN]), (req, res, next) =>
            this._restaurantsController.handleAddRestaurantForUser(req, res, next)
        );

        this.router.post(
            '/:restaurant_id/users/:user_id/add',
            authorize(),
            casl(),
            (req: RequestWithPermissions<AddRestaurantForUserWithEmailParamsDto, any, AddRestaurantForUserWithEmailBodyDto>, res, next) =>
                this._restaurantsController.handleAddRestaurantForUserWithEmail(req, res, next)
        );

        /**
         *
         * @api {post} /:restaurant_id/users/update update UserRestaurant
         * @apiName updateUserRestaurants
         * @apiGroup Restaurant
         *
         * @apiParam {string} restaurant_id Restaurant's unique ID.
         * @apiParam {string} user_id user's id.
         * @apiParamExample {json}
         * {
         *      restaurant_id: '123456',
         * }
         *
         * @apiSuccess {Object}
         *
         * @apiSuccessExample Success-Response:
         *     HTTP/1.1 200 OK
         *     {
         *       "msg": "Success"
         *     }
         *
         * @apiError Bad Request, need restaurant_id.
         * @apiErrorExample {json} Error-Response:
         *     HTTP/1.1 400 Bad Request
         *     {
         *       "msg": {
         *            error: true,
         *            message: 'missing_param'
         *            errorData: 'xxxx'
         *        }
         *     }
         *
         */
        this.router.post('/:restaurant_id/users/update', authorize([Role.ADMIN]), (req, res, next) =>
            this._restaurantsController.handleUpdateUserRestaurants(req, res, next)
        );

        /**
         *
         * Keep this route in api vs service-users to keep service-users clean
         * @api {get} /restaurants/:restaurant_id/user/remove Remove a restaurant from a user's restaurants
         * @apiName RemoveRestaurantForUser
         * @apiGroup Restaurant
         *
         * @apiParam {string} restaurant_id Restaurant's unique ID.
         * @apiParamExample {json}
         * {
         *    restaurant_id: '1234'
         * }
         *
         * @apiSuccess {Object}
         *
         * @apiSuccessExample Success-Response:
         *     HTTP/1.1 200 OK
         *     {
         *       "msg": "Success"
         *     }
         *
         * @apiError Bad Request, need restaurant_id.
         * @apiErrorExample {json} Error-Response:
         *     HTTP/1.1 400 Bad Request
         *     {
         *       "msg": {
         *            error: true,
         *            message: 'missing_param'
         *            errorData: 'xxxx'
         *        }
         *     }
         */
        this.router.get('/:restaurant_id/user/remove', authorize(), (req, res, next) =>
            this._restaurantsController.handleRemoveRestaurantForUser(req, res, next)
        );

        this.router.get('/:restaurant_id/currentState', authorize(), (req, res, next) =>
            this._restaurantsController.handleGetRestaurantCurrentState(req, res, next)
        );

        this.router.get('/:restaurant_id/updateCompletionScore', authorize(), userHasAccessToRestaurant, (req, res, next) =>
            this._restaurantsController.handleUpdateRestaurantCompletionScore(req, res, next)
        );

        this.router.post('/user/remove', authorize(), (req, res, next) =>
            this._restaurantsController.handleRemoveUserRestaurants(req, res, next)
        );

        /**
         * Create restaurant
         * @api {post} / Create restaurant
         * @apiName CreateRestaurant
         * @apiGroup Restaurant
         *
         * @apiSuccess {string} msg
         *
         * @apiError DuplicateRecordError restaurant already exist.
         * @apiErrorExample {json} Error-Response:
         *     HTTP/1.1 404 Not Found
         *     {
         *       "msg": {
         *            error: true,
         *            message: 'DuplicateRecordError'
         *        }
         *     }
         */
        this.router.post('/', authorize([Role.MALOU_FREE]), casl(), (req, res, next) =>
            this._restaurantsController.handleCreateRestaurant(req, res, next)
        );

        /**
         * Get restaurant by id
         */
        this.router.get(
            '/:restaurant_id',
            authorize(),
            isRestaurantActive,
            userHasAccessToRestaurant,
            (req: Request<CheckRestaurantIdInParamsDto>, res, next) => this._restaurantsController.handleGetRestaurantById(req, res, next)
        );

        this.router.put(
            '/:restaurant_id/upsert_from_platform',
            authorize(),
            userHasAccessToRestaurant,
            (req: Request<CheckRestaurantIdInParamsDto>, res, next) =>
                this._restaurantsController.handleFetchPlatformAndUpsertRestaurant(req, res, next)
        );

        /**
         * Update a restaurant
         */
        this.router.put(
            '/:restaurant_id',
            authorize([Role.MALOU_FREE]),
            casl(),
            (req: RequestWithPermissions<UpdateRestaurantParamsDto, never, UpdateRestaurantBodyDto>, res, next) =>
                this._restaurantsController.handleUpdateRestaurant(req, res, next)
        );

        this.router.put(
            '/:restaurant_id/admin/active',
            authorize([Role.ADMIN]),
            userHasAccessToRestaurant,
            (req: Request<CheckRestaurantIdInParamsDto, never, UpdateRestaurantActiveBodyDto>, res, next) =>
                this._restaurantsController.handleUpdateRestaurantActive(req, res, next)
        );

        this.router.put('/:restaurant_id/admin', authorize([Role.ADMIN]), (req, res, next) =>
            this._restaurantsController.handleAdminUpdateRestaurant(req, res, next)
        );

        this.router.put(
            '/:restaurant_id/yext/activate',
            authorize([Role.ADMIN]),
            (req: Request<ActivateYextParamsDto, never, never>, res: Response, next: NextFunction) =>
                this._restaurantsController.handleActivateYextLocation(req, res, next)
        );

        this.router.put(
            '/:restaurant_id/yext/deactivate',
            authorize([Role.ADMIN]),
            (req: Request<DeactivateYextParamsDto, never, never>, res: Response, next: NextFunction) =>
                this._restaurantsController.handleDeactivateYextLocation(req, res, next)
        );

        this.router.put('/:restaurantId/organization', authorize([Role.ADMIN]), (req, res, next) =>
            this._restaurantsController.handleUpdateRestaurantOrganization(req, res, next)
        );

        /**
         *
         * @api {post} /restaurants/:restaurant_id/platforms/:platform_key/access Update platform access for restaurant
         * @apiName UpdatePlatformAccess
         * @apiGroup Restaurant
         *
         * @apiParam {string} restaurant_id Restaurant's unique ID.
         * @apiParam {string} platform_key
         * @apiParamExample {json}
         * {
         *    restaurant_id: '1234',
         *    platform_key: 'tripadvisor'
         *
         * }
         *
         * @apiSuccess {string} msg
         *
         * @apiSuccessExample Success-Response:
         *     HTTP/1.1 200 OK
         *    {
         *        "msg": "Success",
         *         data: Restaurant
         *    }
         *
         * @apiError MissingParameter Missing parameter, need restaurant_id.
         * @apiErrorExample {json} Error-Response:
         *     HTTP/1.1 400 Bad Request
         *     {
         *       "msg": {
         *            error: true,
         *            message: 'missing_param'
         *            errorData: 'restaurant_id is needed'
         *        }
         *     }
         * @apiError NotFound Can't find restaurant.
         * @apiErrorExample {json} Error-Response:
         *     HTTP/1.1 404 Not Found
         *     {
         *       "msg": {
         *            error: true,
         *            message: 'missing_resource'
         *        }
         *     }
         */
        this.router.post(
            '/:restaurant_id/access',
            authorize(),
            userHasAccessToRestaurant,
            (req: Request<CreatePlatformAccessParamsDto, never, CreatePlatformAccessBodyDto>, res: Response, next: NextFunction) =>
                this._restaurantsController.handleCreatePlatformAccess(req, res, next)
        );

        this.router.put(
            '/:restaurant_id/access',
            authorize([Role.ADMIN]),
            (
                req: Request<UpdatePlatformAccessStatusParamsDto, never, UpdatePlatformAccessStatusBodyDto>,
                res: Response,
                next: NextFunction
            ) => this._restaurantsController.handleUpdatePlatformAccessStatus(req, res, next)
        );

        this.router.post(
            '/:restaurant_id/access/validate',
            authorize(),
            userHasAccessToRestaurant,
            (req: Request<HandleValidatePlatformAccessParamsDto, never, HandleValidatePlatformAccessBodyDto>, res, next) =>
                this._restaurantsController.handleValidatePlatformAccess(req, res, next)
        );

        /**
         *
         * @api {get} /restaurants/:restaurant_id/platforms/:platform_key/access Reveal access for this platform
         * @apiName ShowPlatformAccess
         * @apiGroup Restaurant
         *
         * @apiParam {string} restaurant_id Restaurant's unique ID.
         * @apiParam {string} platform_key
         * @apiParamExample {json}
         * {
         *    restaurant_id: '1234',
         *    platform_key: 'tripadvisor'
         *
         * }
         *
         * @apiSuccess {string} msg
         *
         * @apiSuccessExample Success-Response:
         *     HTTP/1.1 200 OK
         *    {
         *        "msg": "Success",
         *         data: String
         *    }
         *
         * @apiError MissingParameter Missing parameter, need restaurant_id.
         * @apiErrorExample {json} Error-Response:
         *     HTTP/1.1 400 Bad Request
         *     {
         *       "msg": {
         *            error: true,
         *            message: 'missing_param'
         *            errorData: 'restaurant_id is needed'
         *        }
         *     }
         * @apiError NotFound Can't find restaurant.
         * @apiErrorExample {json} Error-Response:
         *     HTTP/1.1 404 Not Found
         *     {
         *       "msg": {
         *            error: true,
         *            message: 'missing_resource'
         *        }
         *     }
         */
        this.router.get(
            '/:restaurant_id/platforms/:platform_key/access',
            authorize(),
            userHasAccessToRestaurant,
            (req: Request<HandleShowPlatformAccessParamsDto>, res, next) =>
                this._restaurantsController.handleShowPlatformAccess(req, res, next)
        );

        /**
         * @api {post} /restaurants/:restaurant_id/jobs/bookmarked_post Run job to upload post medias in AWS s3
         * @apiName RunBookmarkedPostJob
         * @apiGroup Restaurant
         *
         * @apiParam {string} restaurant_id Restaurant's unique ID.
         * @apiParamExample {json}
         * {
         *    restaurant_id: '1234',
         * }
         *
         * @apiSuccess {string} msg
         * @apiSuccessExample Success-Response:
         *     HTTP/1.1 200 OK
         *    {
         *        "msg": "Success",
         *         data: Restaurant
         *    }
         *
         * @apiErrorExample {json} Error-Response:
         *     HTTP/1.1 500 Server Error
         *     {
         *       "msg": {
         *            stack: 'path',
         *            status: 500
         *            msg: 'error'
         *        }
         *     }
         */
        this.router.post(
            '/:restaurant_id/jobs/bookmarked_post',
            authorize(),
            userHasAccessToRestaurant,
            (req: Request<CheckRestaurantIdInParamsDto>, res, next) =>
                this._restaurantsController.handleRunBookmarkedPostJob(req, res, next)
        );

        /**
         *
         * @api {get} /organizations/:organization_id Get all organization restaurants
         * @apiName GetOrganizationRestaurants
         * @apiGroup Restaurant
         *
         * @apiSuccess {Object}
         *
         * @apiSuccessExample Success-Response:
         *     HTTP/1.1 200 OK
         *     {
         *       "data": [Restaurant]
         *     }
         */
        this.router.get('/organizations/:organization_id', authorize(), (req, res, next) =>
            this._restaurantsController.handleGetOrganizationRestaurants(req, res, next)
        );

        this.router.get(
            '/:restaurant_id/users',
            authorize(),
            userHasAccessToRestaurant,
            (req: Request<CheckRestaurantIdInParamsDto>, res, next) => this._restaurantsController.getManagers(req, res, next)
        );

        this.router.post('/:restaurantId/update-calendar-events-country', authorize(), (req, res, next) =>
            this._restaurantsController.handleUpdateRestaurantCalendarEventsCountry(req, res, next)
        );

        return this.router;
    }
}
