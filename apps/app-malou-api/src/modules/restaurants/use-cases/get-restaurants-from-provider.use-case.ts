import { singleton } from 'tsyringe';

import {
    GetRestaurantsFromProviderQueryDto,
    GetRestaurantsFromProviderResponseDto,
    HyperlineLocationDto,
    PlatformSearchWithConnectabilityDto,
} from '@malou-io/package-dto';
import { MalouErrorCode, PlatformKey } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import OrganizationsRepository from ':modules/organizations/organizations.repository';
import PlatformsUseCases from ':modules/platforms/platforms.use-cases';
import { MalouRestaurantSearchResult } from ':modules/platforms/use-cases/search-social-ids/search-social-ids.interface';
import { HyperlineProvider } from ':providers/hyperline/hyperline.provider';

enum ConnectableStatus {
    CONNECTABLE = 'CONNECTABLE',
    NOT_CONNECTABLE = 'NOT_CONNECTABLE',
    ALREADY_CONNECTED = 'ALREADY_CONNECTED',
}

@singleton()
export class GetRestaurantsFromProviderUseCase {
    constructor(
        private readonly _platformsUseCases: PlatformsUseCases,
        private readonly _hyperlineProvider: HyperlineProvider,
        private readonly _organizationsRepository: OrganizationsRepository
    ) {}

    async execute(query: GetRestaurantsFromProviderQueryDto): Promise<GetRestaurantsFromProviderResponseDto> {
        const { organizationId, platformKey = PlatformKey.GMB, credentialId } = query;

        try {
            // Get organization to find its providerId
            const organization = await this._organizationsRepository.findOneOrFail({
                filter: { _id: organizationId },
                options: { lean: true },
            });

            if (!organization.providerId) {
                throw new MalouError(MalouErrorCode.ORGANIZATION_NO_PROVIDER_ID, {
                    message: 'Organization does not have a providerId for Hyperline integration',
                    metadata: { organizationId },
                });
            }

            // Fetch platform data (Google/Facebook restaurants)
            const platformData = await this._platformsUseCases.getSocialIds({
                platformKey,
                restaurantId: null,
                credentialId,
            });

            // Fetch Hyperline locations for this organization
            const hyperlineData = await this._hyperlineProvider.getLocationsByOrganization({
                organizationProviderId: organization.providerId,
            });

            // Create a map of placeId to Hyperline location for quick lookup
            const hyperlineLocationMap = new Map<string, HyperlineLocationDto>();
            hyperlineData.locations.forEach((location) => {
                if (location.placeId) {
                    hyperlineLocationMap.set(location.placeId, {
                        id: location.id,
                        name: location.name,
                        placeId: location.placeId,
                        malouRestaurantId: location.malouRestaurantId,
                        isConnectable: location.isConnectable,
                        isAlreadyConnected: location.isAlreadyConnected,
                        address: location.address,
                    });
                }
            });

            // Merge platform data with Hyperline data to determine connectability
            const mergedResults: PlatformSearchWithConnectabilityDto[] = platformData.list.map((platformResult) => {
                const placeId = this._extractPlaceId(platformResult, platformKey);
                const hyperlineLocation = placeId ? hyperlineLocationMap.get(placeId) : undefined;

                let connectableStatus: ConnectableStatus;
                if (hyperlineLocation) {
                    if (hyperlineLocation.isAlreadyConnected) {
                        connectableStatus = ConnectableStatus.ALREADY_CONNECTED;
                    } else if (hyperlineLocation.isConnectable) {
                        connectableStatus = ConnectableStatus.CONNECTABLE;
                    } else {
                        connectableStatus = ConnectableStatus.NOT_CONNECTABLE;
                    }
                } else {
                    connectableStatus = ConnectableStatus.NOT_CONNECTABLE;
                }

                return {
                    name: platformResult.name || '',
                    locationId: platformResult.locationId || platformResult.socialId,
                    formattedAddress: platformResult.formattedAddress || '',
                    picture: platformResult.picture || '',
                    rating: platformResult.rating || 0,
                    socialUrl: platformResult.socialUrl || '',
                    socialId: platformResult.socialId,
                    placeId,
                    connectableStatus,
                    hyperlineLocationId: hyperlineLocation?.id,
                    access: platformResult.access,
                    accountId: platformResult.accountId,
                    accountName: platformResult.accountName,
                    apiEndpointV2: platformResult.apiEndpointV2,
                    pageCategory: platformResult.pageCategory,
                    parentSocialId: platformResult.parentSocialId,
                    hasTransitionedToNewPageExperience: platformResult.hasTransitionedToNewPageExperience,
                    username: platformResult.username,
                    drnId: platformResult.drnId,
                };
            });

            logger.info('[GET_RESTAURANTS_FROM_PROVIDER] Successfully merged platform and Hyperline data', {
                organizationId,
                platformKey,
                platformResultsCount: platformData.list.length,
                hyperlineLocationsCount: hyperlineData.locations.length,
                mergedResultsCount: mergedResults.length,
            });

            return {
                list: mergedResults,
                hyperlineLocations: Array.from(hyperlineLocationMap.values()),
                pagination: platformData.pagination,
            };
        } catch (error: any) {
            logger.error('[GET_RESTAURANTS_FROM_PROVIDER] Error fetching restaurants from provider', {
                organizationId,
                platformKey,
                error: error.message,
            });

            if (error instanceof MalouError) {
                throw error;
            }

            throw new MalouError(MalouErrorCode.EXTERNAL_API_ERROR, {
                message: `Failed to fetch restaurants from provider: ${error.message}`,
                metadata: { organizationId, platformKey, error: error.message },
            });
        }
    }

    private _extractPlaceId(platformResult: MalouRestaurantSearchResult, platformKey: PlatformKey): string | undefined {
        // For Google My Business, the socialId is typically the placeId
        if (platformKey === PlatformKey.GMB) {
            return platformResult.socialId;
        }

        // For Facebook, we might need to look for placeId in metadata or use a different approach
        // This would need to be implemented based on how Facebook data is structured
        if (platformKey === PlatformKey.FACEBOOK) {
            // Facebook pages might have a placeId in their metadata or we might need to
            // use the page's location information to match with Google Places
            return undefined; // TODO: Implement Facebook placeId extraction
        }

        return undefined;
    }
}
