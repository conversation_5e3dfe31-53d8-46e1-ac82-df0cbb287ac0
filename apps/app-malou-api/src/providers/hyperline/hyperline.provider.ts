import axios, { AxiosInstance } from 'axios';
import { singleton } from 'tsyringe';

import { MalouErrorCode } from '@malou-io/package-utils';

import { Config } from ':config';
import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';

import { IHyperlineProvider } from './hyperline.provider.interface';
import {
    GetCustomerWithChildrenRequest,
    GetCustomerWithChildrenResponse,
    GetLocationsByOrganizationRequest,
    GetLocationsByOrganizationResponse,
    HyperlineApiResponse,
    HyperlineCustomer,
    HyperlineLocation,
    HyperlineLocationWithStatus,
} from './hyperline.provider.interfaces';

@singleton()
export class HyperlineProvider implements IHyperlineProvider {
    private _axiosInstance: AxiosInstance;

    constructor() {
        this._axiosInstance = axios.create({
            baseURL: Config.vendors.hyperline.baseUrl,
            headers: {
                Authorization: `Bearer ${Config.vendors.hyperline.apiKey}`,
                'Content-Type': 'application/json',
            },
        });
    }

    async getCustomer(customerId: string): Promise<HyperlineCustomer> {
        try {
            const response = await this._axiosInstance.get<HyperlineApiResponse<HyperlineCustomer>>(`/customers/${customerId}`);
            return response.data.data;
        } catch (error: any) {
            logger.error('[HYPERLINE_PROVIDER] Error fetching customer', { customerId, error: error.message });
            throw new MalouError(MalouErrorCode.EXTERNAL_API_ERROR, {
                metadata: { customerId, error: error.message },
                message: `Failed to fetch customer from Hyperline: ${error.message}`,
            });
        }
    }

    async getCustomerWithChildren(request: GetCustomerWithChildrenRequest): Promise<GetCustomerWithChildrenResponse> {
        try {
            // First, get the main customer
            const customer = await this.getCustomer(request.customerId);

            // Get all children (legal entities)
            const legalEntities: HyperlineCustomer[] = [];
            const locations: HyperlineLocation[] = [];

            if (customer.children && customer.children.length > 0) {
                // Fetch detailed information for each child (legal entity)
                for (const child of customer.children) {
                    const detailedChild = await this.getCustomer(child.id);
                    legalEntities.push(detailedChild);

                    // For each legal entity, get its children (locations)
                    if (detailedChild.children && detailedChild.children.length > 0) {
                        for (const location of detailedChild.children) {
                            const detailedLocation = await this.getCustomer(location.id);
                            // Convert customer to location format
                            const locationData: HyperlineLocation = {
                                id: detailedLocation.id,
                                name: detailedLocation.name,
                                address: detailedLocation.address,
                                placeId: detailedLocation.metadata?.placeId,
                                malouRestaurantId: detailedLocation.metadata?.malouRestaurantId,
                                metadata: detailedLocation.metadata,
                                created_at: detailedLocation.created_at,
                                updated_at: detailedLocation.updated_at,
                            };
                            locations.push(locationData);
                        }
                    }
                }
            }

            return {
                customer,
                legalEntities,
                locations,
            };
        } catch (error: any) {
            logger.error('[HYPERLINE_PROVIDER] Error fetching customer with children', {
                customerId: request.customerId,
                error: error.message,
            });
            throw new MalouError(MalouErrorCode.EXTERNAL_API_ERROR, {
                metadata: { customerId: request.customerId, error: error.message },
                message: `Failed to fetch customer with children from Hyperline: ${error.message}`,
            });
        }
    }

    async getLocationsByOrganization(request: GetLocationsByOrganizationRequest): Promise<GetLocationsByOrganizationResponse> {
        try {
            // Get the customer with the provided organizationProviderId
            const customerResponse = await this.getCustomerWithChildren({
                customerId: request.organizationProviderId,
            });

            // Transform locations to include connectability status
            const locationsWithStatus: HyperlineLocationWithStatus[] = customerResponse.locations.map((location) => ({
                ...location,
                isConnectable: !!location.placeId && !location.malouRestaurantId,
                isAlreadyConnected: !!location.malouRestaurantId,
            }));

            return {
                locations: locationsWithStatus,
            };
        } catch (error: any) {
            logger.error('[HYPERLINE_PROVIDER] Error fetching locations by organization', {
                organizationProviderId: request.organizationProviderId,
                error: error.message,
            });
            throw new MalouError(MalouErrorCode.EXTERNAL_API_ERROR, {
                metadata: { organizationProviderId: request.organizationProviderId, error: error.message },
                message: `Failed to fetch locations by organization from Hyperline: ${error.message}`,
            });
        }
    }
}
