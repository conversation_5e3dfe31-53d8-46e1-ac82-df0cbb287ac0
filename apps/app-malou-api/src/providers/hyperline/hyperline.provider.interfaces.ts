// Hyperline API response interfaces based on https://docs.hyperline.co/api-reference/endpoints/customers/get-customer

export interface HyperlineCustomer {
    id: string;
    name: string;
    email?: string;
    phone?: string;
    address?: HyperlineAddress;
    metadata?: Record<string, any>;
    created_at: string;
    updated_at: string;
    children?: HyperlineCustomer[];
}

export interface HyperlineAddress {
    line1?: string;
    line2?: string;
    city?: string;
    state?: string;
    postal_code?: string;
    country?: string;
}

export interface HyperlineLocation {
    id: string;
    name: string;
    address?: HyperlineAddress;
    placeId?: string; // Google Place ID
    malouRestaurantId?: string; // If already connected to a Malou restaurant
    metadata?: Record<string, any>;
    created_at: string;
    updated_at: string;
}

export interface HyperlineSubscription {
    id: string;
    customer_id: string;
    status: 'active' | 'inactive' | 'cancelled' | 'past_due';
    current_period_start: string;
    current_period_end: string;
    metadata?: Record<string, any>;
    created_at: string;
    updated_at: string;
}

export interface HyperlineApiResponse<T> {
    data: T;
    has_more?: boolean;
    next_cursor?: string;
}

export interface HyperlineErrorResponse {
    error: {
        code: string;
        message: string;
        details?: Record<string, any>;
    };
}

// Request/Response types for our use cases
export interface GetCustomerWithChildrenRequest {
    customerId: string;
}

export interface GetCustomerWithChildrenResponse {
    customer: HyperlineCustomer;
    legalEntities: HyperlineCustomer[];
    locations: HyperlineLocation[];
}

export interface GetLocationsByOrganizationRequest {
    organizationProviderId: string;
}

export interface GetLocationsByOrganizationResponse {
    locations: HyperlineLocationWithStatus[];
}

export interface HyperlineLocationWithStatus extends HyperlineLocation {
    isConnectable: boolean;
    isAlreadyConnected: boolean;
    malouRestaurantId?: string;
}
