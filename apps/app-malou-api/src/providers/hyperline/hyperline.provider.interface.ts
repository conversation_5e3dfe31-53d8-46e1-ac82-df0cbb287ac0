import {
    GetCustomerWithChildrenRequest,
    GetCustomerWithChildrenResponse,
    GetLocationsByOrganizationRequest,
    GetLocationsByOrganizationResponse,
    HyperlineCustomer,
} from './hyperline.provider.interfaces';

export interface IHyperlineProvider {
    /**
     * Get a customer by ID from Hyperline API
     */
    getCustomer(customerId: string): Promise<HyperlineCustomer>;

    /**
     * Get customer with all its children (legal entities and locations)
     */
    getCustomerWithChildren(request: GetCustomerWithChildrenRequest): Promise<GetCustomerWithChildrenResponse>;

    /**
     * Get all locations for an organization by its providerId
     */
    getLocationsByOrganization(request: GetLocationsByOrganizationRequest): Promise<GetLocationsByOrganizationResponse>;
}
