{"$schema": "https://turbo.build/schema.v1.json", "pipeline": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**", "lib/**"]}, "build-clean": {"cache": false, "dependsOn": ["^build-clean"]}, "build-development": {"dependsOn": ["^build-development"], "outputs": ["dist/**", "lib/**"]}, "build-production": {"cache": false, "dependsOn": ["^build-production"]}, "build-staging": {"dependsOn": ["^build-staging"], "outputs": ["dist/**", "lib/**"]}, "e2e": {"cache": false}, "email:dev": {"cache": false}, "format": {"cache": false}, "format:check": {}, "lint": {}, "lint-fix": {"cache": false}, "lint-staged": {}, "start-dev": {"cache": false}, "start-email": {"cache": false}, "start-local": {"cache": false}, "start-production": {"cache": false}, "start-staging": {"cache": false}, "test:integration": {"cache": false}, "test:unit": {"cache": false}, "watch-build": {}}}