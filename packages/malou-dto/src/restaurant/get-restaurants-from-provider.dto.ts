import { z } from 'zod';

import { PlatformKey } from '@malou-io/package-utils';

import { objectIdValidator } from '../utils/validators';

// Request DTOs
export const getRestaurantsFromProviderQueryValidator = z.object({
    organizationId: objectIdValidator,
    platformKey: z.nativeEnum(PlatformKey).optional().default(PlatformKey.GMB),
    credentialId: objectIdValidator.optional(),
});

export type GetRestaurantsFromProviderQueryDto = z.infer<typeof getRestaurantsFromProviderQueryValidator>;

// Response DTOs
export const hyperlineLocationValidator = z.object({
    id: z.string(),
    name: z.string(),
    placeId: z.string().optional(),
    malouRestaurantId: z.string().optional(),
    isConnectable: z.boolean(),
    isAlreadyConnected: z.boolean(),
    address: z.object({
        line1: z.string().optional(),
        line2: z.string().optional(),
        city: z.string().optional(),
        state: z.string().optional(),
        postal_code: z.string().optional(),
        country: z.string().optional(),
    }).optional(),
});

export type HyperlineLocationDto = z.infer<typeof hyperlineLocationValidator>;

export const platformSearchWithConnectabilityValidator = z.object({
    name: z.string(),
    locationId: z.string(),
    formattedAddress: z.string(),
    picture: z.string(),
    rating: z.number(),
    socialUrl: z.string(),
    socialId: z.string(),
    placeId: z.string().optional(),
    connectableStatus: z.enum(['CONNECTABLE', 'NOT_CONNECTABLE', 'ALREADY_CONNECTED']),
    hyperlineLocationId: z.string().optional(),
    access: z.object({
        isValid: z.boolean(),
        missing: z.array(z.string()),
        dataExpiresAt: z.number().optional(),
    }).optional(),
    accountId: z.string().optional(),
    accountName: z.string().optional(),
    apiEndpointV2: z.string().optional(),
    apiEndpoint: z.string().optional(),
    pageCategory: z.string().optional(),
    parentSocialId: z.string().optional(),
    hasTransitionedToNewPageExperience: z.boolean().optional(),
    username: z.string().optional(),
    drnId: z.string().optional(),
});

export type PlatformSearchWithConnectabilityDto = z.infer<typeof platformSearchWithConnectabilityValidator>;

export const getRestaurantsFromProviderResponseValidator = z.object({
    list: z.array(platformSearchWithConnectabilityValidator),
    hyperlineLocations: z.array(hyperlineLocationValidator),
    pagination: z.object({
        total: z.number(),
        count: z.number(),
        limit: z.number(),
        currentPage: z.number(),
        totalPages: z.number(),
        nextPage: z.string(),
        previousPage: z.string(),
    }).optional(),
});

export type GetRestaurantsFromProviderResponseDto = z.infer<typeof getRestaurantsFromProviderResponseValidator>;
